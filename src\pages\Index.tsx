import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import NewMobileMenu from "@/components/pages/NewMobileMenu";
import MobileFooter from "@/components/navigation/MobileFooter";
import PageContainer from "@/components/layout/PageContainer";
import Friends from "./Friends";
import Account from "./Account";
import Shop from "./Shop";
import { useAdBannerVisibility } from "@/hooks/useAdBannerVisibility";
import {
  getGameSettings,
  updateVictoryPoints,
  updateDifficulty,
} from "@/services/gameSettingsService";
import {
  shouldShowReviewRequest,
  recordReviewRequestShown,
  showNativeReviewModal,
} from "@/services/reviewTrackingService";

interface IndexProps {
  initialTab?: "home" | "profile" | "friends" | "shop";
}

// Funzione helper per determinare la pagina dall'URL
const getCurrentPageFromUrl = (
  pathname: string
): "home" | "profile" | "friends" | "shop" => {
  switch (pathname) {
    case "/account":
      return "profile";
    case "/friends":
      return "friends";
    case "/shop":
      return "shop";
    case "/":
    default:
      return "home";
  }
};

const Index = ({ initialTab = "home" }: IndexProps) => {
  // Carica le impostazioni salvate
  const savedSettings = getGameSettings();
  const { shouldShowBanner } = useAdBannerVisibility();

  const [difficulty, setDifficulty] = useState<"easy" | "medium" | "hard">(
    savedSettings.difficulty
  );
  const [victoryPoints, setVictoryPoints] = useState<"21" | "31" | "41">(
    savedSettings.victoryPoints
  );
  const navigate = useNavigate();
  const location = useLocation();
  const [resetKey, setResetKey] = useState(Date.now().toString());

  const [currentPage, setCurrentPage] = useState<
    "home" | "profile" | "friends" | "shop"
  >(getCurrentPageFromUrl(location.pathname));

  // Stati per gestire le animazioni di transizione
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [animationClass, setAnimationClass] = useState("");

  // Handlers per salvare le impostazioni quando cambiano
  const handleDifficultyChange = (
    newDifficulty: "easy" | "medium" | "hard"
  ) => {
    setDifficulty(newDifficulty);
    updateDifficulty(newDifficulty);
  };

  const handleVictoryPointsChange = (newVictoryPoints: "21" | "31" | "41") => {
    setVictoryPoints(newVictoryPoints);
    updateVictoryPoints(newVictoryPoints);
  };

  // 🔄 LISTENER PER SINCRONIZZAZIONE IMPOSTAZIONI
  useEffect(() => {
    const handleSettingsChange = () => {
      const updatedSettings = getGameSettings();

      // Controlla se ci sono effettivamente cambiamenti prima di loggare
      const hasChanges =
        updatedSettings.victoryPoints !== victoryPoints ||
        updatedSettings.difficulty !== difficulty;

      if (hasChanges) {
        // Aggiorna solo se i valori sono effettivamente cambiati
        if (updatedSettings.victoryPoints !== victoryPoints) {
          setVictoryPoints(updatedSettings.victoryPoints);
        }
        if (updatedSettings.difficulty !== difficulty) {
          setDifficulty(updatedSettings.difficulty);
        }
      }
    };

    // Listener per cambiamenti nelle impostazioni
    window.addEventListener("gameSettingsChanged", handleSettingsChange);

    return () => {
      window.removeEventListener("gameSettingsChanged", handleSettingsChange);
    };
  }, [victoryPoints, difficulty]);
  // Handle returning from game page or rules page
  useEffect(() => {
    // If we're returning from the game page, reset state properly
    if (location.state?.fromGame) {
      // Force a full UI refresh by updating the reset key
      setResetKey(Date.now().toString());

      // Clean up location state to prevent re-triggering
      navigate(location.pathname, { replace: true, state: {} });
    }

    // Check for review request when returning from game or rules
    const checkReviewRequest = async () => {
      if (location.state?.fromGame || location.state?.fromRules) {
        console.log(
          "🏠 Tornato alla home da:",
          location.state.fromGame ? "partita" : "regole"
        );

        setTimeout(async () => {
          const shouldShow = shouldShowReviewRequest();
          console.log(
            "🎯 Controllo recensione dalla home, risultato:",
            shouldShow
          );

          if (shouldShow) {
            console.log("⭐ Mostrando richiesta recensione nativa dalla home");
            recordReviewRequestShown();
            const result = await showNativeReviewModal();
            console.log("📝 Risultato modale recensione dalla home:", result);
          } else {
            console.log("📝 Recensione non necessaria al momento dalla home");
          }
        }, 1500); // Delay maggiore per permettere la transizione
      }
    };

    checkReviewRequest();
  }, [location, navigate]); // Sincronizza currentPage con l'URL quando cambia la location
  useEffect(() => {
    const pageFromUrl = getCurrentPageFromUrl(location.pathname);
    if (pageFromUrl !== currentPage) {
      setCurrentPage(pageFromUrl);
    }
  }, [location.pathname, currentPage]);
  // Ordine delle pagine per determinare la direzione dell'animazione
  // Ordine corretto: negozio -> gioca -> amici -> profilo
  const pageOrder = ["shop", "home", "friends", "profile"];

  const getPageIndex = (page: "home" | "profile" | "friends" | "shop") => {
    return pageOrder.indexOf(page);
  };

  const handlePageChange = (page: "home" | "profile" | "friends" | "shop") => {
    if (page === currentPage || isTransitioning) return;

    // Disabilita temporaneamente le animazioni per ridurre il lag
    setIsTransitioning(true);

    // Naviga immediatamente senza animazioni
    switch (page) {
      case "home":
        navigate("/");
        break;
      case "profile":
        navigate("/account");
        break;
      case "friends":
        navigate("/friends");
        break;
      case "shop":
        navigate("/shop");
        break;
    }

    // Termina immediatamente la transizione senza animazioni
    setTimeout(() => {
      setIsTransitioning(false);
      setAnimationClass("");
    }, 50); // Timeout minimo per evitare problemi di stato
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case "home":
        return (
          <NewMobileMenu
            difficulty={difficulty}
            setDifficulty={handleDifficultyChange}
            victoryPoints={victoryPoints}
            setVictoryPoints={handleVictoryPointsChange}
          />
        );
      case "profile":
        return <Account />;
      case "friends":
        return <Friends />;
      case "shop":
        return <Shop />;
      default:
        return (
          <NewMobileMenu
            difficulty={difficulty}
            setDifficulty={handleDifficultyChange}
            victoryPoints={victoryPoints}
            setVictoryPoints={handleVictoryPointsChange}
          />
        );
    }
  };
  return (
    <div className="min-h-screen flex flex-col" key={resetKey}>
      <PageContainer footerHeight={67}>
        {/* Rimosso animationClass temporaneamente per ridurre lag */}
        <div>{renderCurrentPage()}</div>
      </PageContainer>
      <MobileFooter currentPage={currentPage} onPageChange={handlePageChange} />
    </div>
  );
};

export default Index;
