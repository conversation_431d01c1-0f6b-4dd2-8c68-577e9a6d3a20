<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover"
    />
    <title>Marafone Romagnolo</title>
    <meta name="description" content="Gioco di carte tradizionale romagnolo" />
    <meta name="author" content="Elia Z<PERSON>" />
    <meta property="og:image" content="/images/logos/logo_bastoni.png" />
    <meta name="theme-color" content="#7F1D1D" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <link rel="manifest" href="/manifest.webmanifest" />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/images/logos/logo_bastoni_compressed.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/images/logos/logo_bastoni_compressed.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="192x192"
      href="/images/logos/logo_bastoni.png"
    />
    <link rel="shortcut icon" href="/favicon.png" />
    <!-- Google Fonts: DynaPuff, Fredoka, Luckiest Guy -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=DynaPuff:wght@400..700&family=Fredoka:wght@300..700&family=Luckiest+Guy&display=swap"
      rel="stylesheet"
    />
    <script type="module" crossorigin src="/assets/index-B548sXbe.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-COrNHRvO.js">
    <link rel="modulepreload" crossorigin href="/assets/audio-5UOY9KLB.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-CVF02LcF.js">
    <link rel="modulepreload" crossorigin href="/assets/supabase-DLIhrfaA.js">
    <link rel="modulepreload" crossorigin href="/assets/audioManager-oMWoQbcF.js">
    <link rel="modulepreload" crossorigin href="/assets/game-DnO6rJ4w.js">
    <link rel="stylesheet" crossorigin href="/assets/index-DSvqDNDU.css">
  </head>
  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script>
      // 🛡️ Protezione per errori GPT Engineer
      (function () {
        const originalConsoleError = console.error;
        console.error = function (...args) {
          const message = args.join(" ");
          if (
            message.includes("runtime.lastError") ||
            message.includes("message channel closed") ||
            message.includes("listener indicated an asynchronous response")
          ) {
            // Ignora questi warning specifici
            return;
          }
          originalConsoleError.apply(console, args);
        };
      })();
    </script>
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>

  </body>
</html>
