import{j as e}from"./ui-CVF02LcF.js";import{r as o}from"./vendor-COrNHRvO.js";import{G as a}from"./index-B548sXbe.js";import"./audio-5UOY9KLB.js";import"./supabase-DLIhrfaA.js";import"./game-DnO6rJ4w.js";import"./audioManager-oMWoQbcF.js";const m={gameScore:[1,18]},u=()=>{const[t,r]=o.useState(!0);return e.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-yellow-100 to-orange-100",children:[e.jsx("button",{className:"mb-6 px-6 py-3 rounded-xl bg-amber-600 text-white font-bold shadow hover:bg-amber-700 transition",onClick:()=>r(!0),children:"Mostra GameOverModal"}),e.jsx(a,{isOpen:t,gameState:m,onStartNewGame:()=>alert("Nuova partita!"),onReturnToMenu:()=>alert("Torna alla home!"),isOnlineMode:!1,difficulty:"medium"})]})};export{u as default};
