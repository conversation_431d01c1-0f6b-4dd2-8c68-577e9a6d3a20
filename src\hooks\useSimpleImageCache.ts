/**
 * 🎯 HOOK SEMPLIFICATO PER CACHE IMMAGINI
 * Sostituisce useImageOptimization con logica più semplice
 */

import { useEffect, useState } from "react";
import { unifiedImageCache } from "@/utils/ui/unifiedImageCache";

interface UseSimpleImageCacheOptions {
  /**
   * Se attivare automaticamente il precaricamento
   */
  autoPreload?: boolean;

  /**
   * Callback quando il precaricamento è completato
   */
  onComplete?: () => void;

  /**
   * Se mostrare i log di debug
   */
  debug?: boolean;
}

interface CacheState {
  isLoading: boolean;
  isComplete: boolean;
  progress: number;
  stats: {
    cachedImages: number;
    loadingImages: number;
    preloadProgress: number;
    totalImages: number;
  };
}

/**
 * Hook semplificato per gestire la cache delle immagini
 */
export const useSimpleImageCache = ({
  autoPreload = true,
  onComplete,
  debug = false,
}: UseSimpleImageCacheOptions = {}) => {
  const [state, setState] = useState<CacheState>({
    isLoading: false,
    isComplete: false,
    progress: 0,
    stats: {
      cachedImages: 0,
      loadingImages: 0,
      preloadProgress: 0,
      totalImages: 0,
    },
  });

  // Aggiorna statistiche periodicamente durante il caricamento
  useEffect(() => {
    if (!state.isLoading) return;

    const interval = setInterval(() => {
      const stats = unifiedImageCache.getStats();
      const progress =
        stats.totalImages > 0
          ? (stats.preloadProgress / stats.totalImages) * 100
          : 0;

      setState((prev) => ({
        ...prev,
        progress,
        stats,
        isComplete: progress >= 100,
      }));

      // 🎯 SOLO LOG SE C'È PROGRESSO REALE O CAMBIO SIGNIFICATIVO
      if (debug && stats.totalImages > 0 && progress > 0) {
        console.log(`📊 Cache progress: ${progress.toFixed(1)}%`, stats);
      }

      // Se completato, ferma l'interval
      if (progress >= 100) {
        setState((prev) => ({ ...prev, isLoading: false, isComplete: true }));
        onComplete?.();
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [state.isLoading, onComplete, debug]);

  // Avvia precaricamento automatico
  useEffect(() => {
    if (!autoPreload) return;

    const startPreloading = async () => {
      if (debug) {
        console.log("🚀 Avvio precaricamento automatico...");
      }

      setState((prev) => ({ ...prev, isLoading: true, isComplete: false }));

      try {
        await unifiedImageCache.preloadAllImages();
      } catch (error) {
        console.error("❌ Errore durante precaricamento:", error);
        setState((prev) => ({ ...prev, isLoading: false }));
      }
    };

    startPreloading();
  }, [autoPreload, debug]);

  // Funzioni di controllo manuale
  const startPreloading = async () => {
    setState((prev) => ({ ...prev, isLoading: true, isComplete: false }));
    try {
      await unifiedImageCache.preloadAllImages();
    } catch (error) {
      console.error("❌ Errore durante precaricamento:", error);
      setState((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const clearCache = () => {
    unifiedImageCache.clearCache();
    setState({
      isLoading: false,
      isComplete: false,
      progress: 0,
      stats: {
        cachedImages: 0,
        loadingImages: 0,
        preloadProgress: 0,
        totalImages: 0,
      },
    });
  };

  const isImageReady = (src: string): boolean => {
    return unifiedImageCache.isImageReady(src);
  };

  const loadImage = async (src: string): Promise<HTMLImageElement> => {
    return unifiedImageCache.loadImage(src);
  };

  return {
    // Stato
    isLoading: state.isLoading,
    isComplete: state.isComplete,
    progress: state.progress,
    stats: state.stats,

    // Funzioni
    startPreloading,
    clearCache,
    isImageReady,
    loadImage,
  };
};
