import{W as o}from"./game-DnO6rJ4w.js";import{A as n}from"./index-B548sXbe.js";import"./audioManager-oMWoQbcF.js";import"./audio-5UOY9KLB.js";import"./vendor-COrNHRvO.js";import"./ui-CVF02LcF.js";import"./supabase-DLIhrfaA.js";class d extends o{async initialize(){console.log("initialize")}async requestTrackingAuthorization(){console.log("requestTrackingAuthorization")}async trackingAuthorizationStatus(){return{status:"authorized"}}async requestConsentInfo(e){return console.log("requestConsentInfo",e),{status:n.REQUIRED,isConsentFormAvailable:!0}}async showConsentForm(){return console.log("showConsentForm"),{status:n.REQUIRED}}async resetConsentInfo(){console.log("resetConsentInfo")}async setApplicationMuted(e){console.log("setApplicationMuted",e)}async setApplicationVolume(e){console.log("setApplicationVolume",e)}async showBanner(e){console.log("showBanner",e)}async hideBanner(){console.log("hideBanner")}async resumeBanner(){console.log("resumeBanner")}async removeBanner(){console.log("removeBanner")}async prepareInterstitial(e){return console.log("prepareInterstitial",e),{adUnitId:e.adId}}async showInterstitial(){console.log("showInterstitial")}async prepareRewardVideoAd(e){return console.log(e),{adUnitId:e.adId}}async showRewardVideoAd(){return{type:"",amount:0}}async prepareRewardInterstitialAd(e){return console.log(e),{adUnitId:e.adId}}async showRewardInterstitialAd(){return{type:"",amount:0}}}export{d as AdMobWeb};
