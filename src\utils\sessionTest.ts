/**
 * 🧪 UTILITÀ DI TEST PER IL SISTEMA DI SESSIONE
 * Funzioni per testare e validare il nuovo sistema di gestione sessioni
 */

import { sessionManager } from "@/integrations/supabase/client";

// Interfaccia per i risultati dei test
interface TestResult {
  name: string;
  success: boolean;
  message: string;
  duration: number;
}

// Test del SessionManager
export const testSessionManager = async (): Promise<TestResult[]> => {
  const results: TestResult[] = [];

  // Test 1: Verifica che il SessionManager sia inizializzato
  const test1Start = Date.now();
  try {
    const isInitialized =
      sessionManager !== null && sessionManager !== undefined;
    results.push({
      name: "SessionManager Initialization",
      success: isInitialized,
      message: isInitialized
        ? "SessionManager inizializzato correttamente"
        : "SessionManager non inizializzato",
      duration: Date.now() - test1Start,
    });
  } catch (error) {
    results.push({
      name: "SessionManager Initialization",
      success: false,
      message: `Errore inizializzazione: ${error}`,
      duration: Date.now() - test1Start,
    });
  }

  // Test 2: Verifica ensureValidSession
  const test2Start = Date.now();
  try {
    const session = await sessionManager.ensureValidSession();
    const hasSession = session !== null;
    results.push({
      name: "Ensure Valid Session",
      success: true,
      message: hasSession
        ? "Sessione valida ottenuta"
        : "Nessuna sessione (normale se non loggato)",
      duration: Date.now() - test2Start,
    });
  } catch (error) {
    results.push({
      name: "Ensure Valid Session",
      success: false,
      message: `Errore verifica sessione: ${error}`,
      duration: Date.now() - test2Start,
    });
  }

  // Test 3: Test refresh session (solo se c'è una sessione)
  const test3Start = Date.now();
  try {
    const currentSession = await sessionManager.ensureValidSession();
    if (currentSession) {
      const refreshedSession = await sessionManager.refreshSession();
      const refreshSuccess = refreshedSession !== null;
      results.push({
        name: "Session Refresh",
        success: refreshSuccess,
        message: refreshSuccess
          ? "Refresh sessione riuscito"
          : "Refresh sessione fallito",
        duration: Date.now() - test3Start,
      });
    } else {
      results.push({
        name: "Session Refresh",
        success: true,
        message: "Skip test refresh - nessuna sessione attiva",
        duration: Date.now() - test3Start,
      });
    }
  } catch (error) {
    results.push({
      name: "Session Refresh",
      success: false,
      message: `Errore refresh: ${error}`,
      duration: Date.now() - test3Start,
    });
  }

  return results;
};

// Test delle chiamate API con retry
export const testApiCallsWithRetry = async (): Promise<TestResult[]> => {
  const results: TestResult[] = [];

  // Test 1: Import del sistema retry
  const test1Start = Date.now();
  try {
    const { retryWithRefresh } = await import("@/integrations/supabase/client");
    const isFunction = typeof retryWithRefresh === "function";
    results.push({
      name: "Retry System Import",
      success: isFunction,
      message: isFunction
        ? "Sistema retry importato correttamente"
        : "Sistema retry non è una funzione",
      duration: Date.now() - test1Start,
    });
  } catch (error) {
    results.push({
      name: "Retry System Import",
      success: false,
      message: `Errore import retry system: ${error}`,
      duration: Date.now() - test1Start,
    });
  }

  // Test 2: Test operazione semplice con retry
  const test2Start = Date.now();
  try {
    const { retryWithRefresh } = await import("@/integrations/supabase/client");

    // Operazione di test che dovrebbe sempre riuscire
    const result = await retryWithRefresh(async () => {
      return { test: "success" };
    });

    const success = result && result.test === "success";
    results.push({
      name: "Simple Retry Operation",
      success: success,
      message: success
        ? "Operazione retry semplice riuscita"
        : "Operazione retry semplice fallita",
      duration: Date.now() - test2Start,
    });
  } catch (error) {
    results.push({
      name: "Simple Retry Operation",
      success: false,
      message: `Errore operazione retry: ${error}`,
      duration: Date.now() - test2Start,
    });
  }

  return results;
};

// Funzione per eseguire tutti i test
export const runAllSessionTests = async (): Promise<void> => {
  console.log("🧪 Inizio test sistema sessione...");

  const sessionTests = await testSessionManager();
  const apiTests = await testApiCallsWithRetry();

  const allTests = [...sessionTests, ...apiTests];

  console.log("📊 Risultati test sessione:");
  allTests.forEach((test) => {
    const icon = test.success ? "✅" : "❌";
    console.log(`${icon} ${test.name}: ${test.message} (${test.duration}ms)`);
  });

  const successCount = allTests.filter((t) => t.success).length;
  const totalCount = allTests.length;

  console.log(`🎯 Test completati: ${successCount}/${totalCount} riusciti`);

  if (successCount === totalCount) {
    console.log("🎉 Tutti i test sono passati! Sistema sessione funzionante.");
  } else {
    console.warn("⚠️ Alcuni test sono falliti. Controllare la configurazione.");
  }
};

// Funzione per test rapido da console
export const quickSessionTest = async (): Promise<boolean> => {
  try {
    console.log("🧪 Quick test sessione - inizio...");
    const startTime = Date.now();

    const session = await sessionManager.ensureValidSession();
    const duration = Date.now() - startTime;

    console.log(
      `🔍 Quick test sessione completato in ${duration}ms:`,
      session ? "✅ OK" : "⚠️ Nessuna sessione"
    );

    if (session) {
      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const now = Date.now();
      const minutesUntilExpiry = Math.round((expiresAt - now) / 60000);
      console.log(`⏰ Token scade tra ${minutesUntilExpiry} minuti`);
    }

    return true;
  } catch (error) {
    console.error("❌ Quick test fallito:", error);
    return false;
  }
};

// Test specifico per il problema degli 8 minuti
export const testApiCallAfterTime = async (): Promise<boolean> => {
  try {
    console.log("🧪 Test chiamata API con gestione sessione...");

    const { retryWithRefresh, supabase } = await import(
      "@/integrations/supabase/client"
    );

    // Test semplificato: prova a fare una chiamata diretta
    const result = await retryWithRefresh(async () => {
      // Usa direttamente supabase per evitare il SessionManager
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error("Nessuna sessione valida disponibile");
      }

      console.log("✅ Chiamata API simulata riuscita");
      return { success: true, timestamp: Date.now(), tokenValid: true };
    });

    console.log("🎉 Test API call completato:", result);
    return true;
  } catch (error) {
    console.error("❌ Test API call fallito:", error);
    return false;
  }
};

// Test fallback diretto senza SessionManager
export const testDirectApiCall = async (): Promise<boolean> => {
  try {
    console.log("🧪 Test chiamata API diretta (senza SessionManager)...");

    const { supabase } = await import("@/integrations/supabase/client");

    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (error) {
      console.error("❌ Errore getSession:", error);
      return false;
    }

    if (!session?.access_token) {
      console.warn("⚠️ Nessuna sessione attiva");
      return false;
    }

    const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
    const now = Date.now();
    const minutesUntilExpiry = Math.round((expiresAt - now) / 60000);

    console.log(
      `✅ Sessione diretta OK - scade tra ${minutesUntilExpiry} minuti`
    );
    return true;
  } catch (error) {
    console.error("❌ Test diretto fallito:", error);
    return false;
  }
};

// Test SUPER SEMPLIFICATO - chiamata API diretta
export const testSimpleApiCall = async (): Promise<boolean> => {
  try {
    console.log("🚀 Test SUPER SEMPLIFICATO - chiamata API diretta...");

    // Prova a fare una chiamata HTTP semplice
    const response = await fetch("https://httpbin.org/get", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    console.log("✅ Chiamata HTTP semplice riuscita:", data.url);
    return true;
  } catch (error) {
    console.error("❌ Test semplice fallito:", error);
    return false;
  }
};

// Test refresh sessione
export const testSessionRefresh = async (): Promise<boolean> => {
  try {
    console.log("🔄 Test refresh sessione...");

    const { supabase } = await import("@/integrations/supabase/client");

    // Ottieni sessione corrente
    const { data: currentData } = await supabase.auth.getSession();
    if (!currentData?.session) {
      console.warn("⚠️ Nessuna sessione corrente");
      return false;
    }

    const oldToken = currentData.session.access_token;
    console.log("📋 Token corrente:", oldToken.substring(0, 20) + "...");

    // Prova refresh
    const { data: refreshData, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error("❌ Errore refresh:", error);
      return false;
    }

    if (!refreshData?.session) {
      console.error("❌ Nessuna sessione dopo refresh");
      return false;
    }

    const newToken = refreshData.session.access_token;
    console.log("📋 Nuovo token:", newToken.substring(0, 20) + "...");

    const tokenChanged = oldToken !== newToken;
    console.log(`✅ Refresh completato - Token cambiato: ${tokenChanged}`);

    return true;
  } catch (error) {
    console.error("❌ Test refresh fallito:", error);
    return false;
  }
};
