var Ee=Object.defineProperty;var Pe=(r,e,n)=>e in r?Ee(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n;var ee=(r,e,n)=>Pe(r,typeof e!="symbol"?e+"":e,n);import{a as re}from"./audioManager-oMWoQbcF.js";/*! Capacitor: https://capacitorjs.com/ - MIT License */var H;(function(r){r.Unimplemented="UNIMPLEMENTED",r.Unavailable="UNAVAILABLE"})(H||(H={}));class ne extends Error{constructor(e,n,o){super(e),this.message=e,this.code=n,this.data=o}}const Ne=r=>{var e,n;return r!=null&&r.androidBridge?"android":!((n=(e=r==null?void 0:r.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||n===void 0)&&n.bridge?"ios":"web"},ve=r=>{const e=r.CapacitorCustomPlatform||null,n=r.Capacitor||{},o=n.Plugins=n.Plugins||{},t=()=>e!==null?e.name:Ne(r),i=()=>t()!=="web",a=c=>{const d=g.get(c);return!!(d!=null&&d.platforms.has(t())||s(c))},s=c=>{var d;return(d=n.PluginHeaders)===null||d===void 0?void 0:d.find(m=>m.name===c)},u=c=>r.console.error(c),g=new Map,l=(c,d={})=>{const m=g.get(c);if(m)return console.warn(`Capacitor plugin "${c}" already registered. Cannot register plugins twice.`),m.proxy;const h=t(),f=s(c);let C;const P=async()=>(!C&&h in d?C=typeof d[h]=="function"?C=await d[h]():C=d[h]:e!==null&&!C&&"web"in d&&(C=typeof d.web=="function"?C=await d.web():C=d.web),C),O=(V,v)=>{var T,E;if(f){const y=f==null?void 0:f.methods.find($=>v===$.name);if(y)return y.rtype==="promise"?$=>n.nativePromise(c,v.toString(),$):($,p)=>n.nativeCallback(c,v.toString(),$,p);if(V)return(T=V[v])===null||T===void 0?void 0:T.bind(V)}else{if(V)return(E=V[v])===null||E===void 0?void 0:E.bind(V);throw new ne(`"${c}" plugin is not implemented on ${h}`,H.Unimplemented)}},k=V=>{let v;const T=(...E)=>{const y=P().then($=>{const p=O($,V);if(p){const A=p(...E);return v=A==null?void 0:A.remove,A}else throw new ne(`"${c}.${V}()" is not implemented on ${h}`,H.Unimplemented)});return V==="addListener"&&(y.remove=async()=>v()),y};return T.toString=()=>`${V.toString()}() { [capacitor code] }`,Object.defineProperty(T,"name",{value:V,writable:!1,configurable:!1}),T},M=k("addListener"),w=k("removeListener"),N=(V,v)=>{const T=M({eventName:V},v),E=async()=>{const $=await T;w({eventName:V,callbackId:$},v)},y=new Promise($=>T.then(()=>$({remove:E})));return y.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await E()},y},L=new Proxy({},{get(V,v){switch(v){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return f?N:M;case"removeListener":return w;default:return k(v)}}});return o[c]=L,g.set(c,{name:c,proxy:L,platforms:new Set([...Object.keys(d),...f?[h]:[]])}),L};return n.convertFileSrc||(n.convertFileSrc=c=>c),n.getPlatform=t,n.handleError=u,n.isNativePlatform=i,n.isPluginAvailable=a,n.registerPlugin=l,n.Exception=ne,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Le=r=>r.Capacitor=ve(r),q=Le(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),pe=q.registerPlugin;class Ae{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,n){let o=!1;this.listeners[e]||(this.listeners[e]=[],o=!0),this.listeners[e].push(n);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),o&&this.sendRetainedArgumentsForEvent(e);const a=async()=>this.removeListener(e,n);return Promise.resolve({remove:a})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,n,o){const t=this.listeners[e];if(!t){if(o){let i=this.retainedEventArguments[e];i||(i=[]),i.push(n),this.retainedEventArguments[e]=i}return}t.forEach(i=>i(n))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,n){this.windowListeners[n]={registered:!1,windowEventName:e,pluginEventName:n,handler:o=>{this.notifyListeners(n,o)}}}unimplemented(e="not implemented"){return new q.Exception(e,H.Unimplemented)}unavailable(e="not available"){return new q.Exception(e,H.Unavailable)}async removeListener(e,n){const o=this.listeners[e];if(!o)return;const t=o.indexOf(n);this.listeners[e].splice(t,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const n=this.retainedEventArguments[e];n&&(delete this.retainedEventArguments[e],n.forEach(o=>{this.notifyListeners(e,o)}))}}const ce=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),le=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ve extends Ae{async getCookies(){const e=document.cookie,n={};return e.split(";").forEach(o=>{if(o.length<=0)return;let[t,i]=o.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");t=le(t).trim(),i=le(i).trim(),n[t]=i}),n}async setCookie(e){try{const n=ce(e.key),o=ce(e.value),t=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),a=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${n}=${o||""}${t}; path=${i}; ${a};`}catch(n){return Promise.reject(n)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(n){return Promise.reject(n)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const n of e)document.cookie=n.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}pe("CapacitorCookies",{web:()=>new Ve});const be=async r=>new Promise((e,n)=>{const o=new FileReader;o.onload=()=>{const t=o.result;e(t.indexOf(",")>=0?t.split(",")[1]:t)},o.onerror=t=>n(t),o.readAsDataURL(r)}),we=(r={})=>{const e=Object.keys(r);return Object.keys(r).map(t=>t.toLocaleLowerCase()).reduce((t,i,a)=>(t[i]=r[e[a]],t),{})},Me=(r,e=!0)=>r?Object.entries(r).reduce((o,t)=>{const[i,a]=t;let s,u;return Array.isArray(a)?(u="",a.forEach(g=>{s=e?encodeURIComponent(g):g,u+=`${i}=${s}&`}),u.slice(0,-1)):(s=e?encodeURIComponent(a):a,u=`${i}=${s}`),`${o}&${u}`},"").substr(1):null,Ue=(r,e={})=>{const n=Object.assign({method:r.method||"GET",headers:r.headers},e),t=we(r.headers)["content-type"]||"";if(typeof r.data=="string")n.body=r.data;else if(t.includes("application/x-www-form-urlencoded")){const i=new URLSearchParams;for(const[a,s]of Object.entries(r.data||{}))i.set(a,s);n.body=i.toString()}else if(t.includes("multipart/form-data")||r.data instanceof FormData){const i=new FormData;if(r.data instanceof FormData)r.data.forEach((s,u)=>{i.append(u,s)});else for(const s of Object.keys(r.data))i.append(s,r.data[s]);n.body=i;const a=new Headers(n.headers);a.delete("content-type"),n.headers=a}else(t.includes("application/json")||typeof r.data=="object")&&(n.body=JSON.stringify(r.data));return n};class Fe extends Ae{async request(e){const n=Ue(e,e.webFetchExtra),o=Me(e.params,e.shouldEncodeUrlParams),t=o?`${e.url}?${o}`:e.url,i=await fetch(t,n),a=i.headers.get("content-type")||"";let{responseType:s="text"}=i.ok?e:{};a.includes("application/json")&&(s="json");let u,g;switch(s){case"arraybuffer":case"blob":g=await i.blob(),u=await be(g);break;case"json":u=await i.json();break;case"document":case"text":default:u=await i.text()}const l={};return i.headers.forEach((c,d)=>{l[d]=c}),{data:u,headers:l,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}pe("CapacitorHttp",{web:()=>new Fe});var U=(r=>(r.Coins="coins",r.Cups="cups",r.Swords="swords",r.Clubs="clubs",r))(U||{}),R=(r=>(r.Ace="A",r.Two="2",r.Three="3",r.Four="4",r.Five="5",r.Six="6",r.Seven="7",r.Jack="J",r.Horse="H",r.King="K",r))(R||{});const We={coins:"Denari",cups:"Coppe",swords:"Spade",clubs:"Bastoni"},Be={A:"asso",3:"tre",2:"due",K:"re",H:"cavallo",J:"fante",7:"setta",6:"sei",5:"cinque",4:"quattro"},ae=()=>{const r=[];return Object.values(U).forEach(e=>{Object.values(R).forEach(n=>{let o=0,t=0;switch(n){case"3":o=10,t=3;break;case"2":o=9,t=3;break;case"A":o=8,t=10;break;case"K":o=7,t=3;break;case"H":o=6,t=3;break;case"J":o=5,t=3;break;case"7":o=4,t=0;break;case"6":o=3,t=0;break;case"5":o=2,t=0;break;case"4":o=1,t=0;break}const i=`${Be[n]} di ${We[e]}`,a=Ge();r.push({id:a,suit:e,rank:n,displayName:i,order:o,value:t})})}),r},he=r=>{const e=[...r];for(let n=e.length-1;n>0;n--){const o=Math.floor(Math.random()*(n+1));[e[n],e[o]]=[e[o],e[n]]}return e},Ce=(r,e,n)=>{const o=Array(e).fill([]).map(()=>[]);for(let t=0;t<n;t++)for(let i=0;i<e;i++)if(r.length>0){const a=r.pop();o[i]=[...o[i],a]}return o},De=(r,e)=>{if(!e)return r;const n=r.filter(o=>o.suit===e);return n.length>0?n:r},xe=(r,e,n)=>{if(r.length===0)throw new Error("Cannot determine winner of empty trick");const o=r.filter(i=>i.suit===n);if(o.length>0)return o.reduce((i,a)=>a.order>i.order?a:i,o[0]);const t=r.filter(i=>i.suit===e);return t.length>0?t.reduce((i,a)=>a.order>i.order?a:i,t[0]):r.reduce((i,a)=>a.order>i.order?a:i,r[0])},Ge=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){const e=Math.random()*16|0;return(r==="x"?e:e&3|8).toString(16)}),Lr={coins:"/images/semi/denari.png",cups:"/images/semi/coppe.png",swords:"/images/semi/spade.png",clubs:"/images/semi/bastoni.png"},Vr=(r,e,n)=>{if(!e.trumpSuit||r.suit!==e.trumpSuit||r.rank!==R.Ace||e.lastTrumpSelector!==n||e.trickNumber!==1)return!1;const o=e.players[n].hand;return Te(o,e.trumpSuit)},Te=(r,e)=>[R.Ace,R.Two,R.Three].every(o=>r.some(t=>t.suit===e&&t.rank===o)),te=(r,e)=>["A","2","3"].every(o=>r.some(t=>t.suit===e&&t.rank===o)),Y=()=>!q.isNativePlatform(),br=()=>{Y()&&console.warn(`🔍 DEBUG MODE ATTIVO!

Puoi vedere le carte di tutti i giocatori.
Per disattivare, imposta VITE_DEBUG_SHOW_ALL_CARDS=false nel file .env

Questo è utile per:
- Capire la logica delle decisioni CPU
- Verificare se l'AI risponde correttamente agli assi
- Testare le strategie collaborative
- Debug delle strategie anti-spreco
- Forzare la maraffa per testare la logica`)},de=()=>Y()?localStorage.getItem("debug_force_player_maraffa")==="true":!1,ge=()=>Y()?localStorage.getItem("debug_force_cpu_maraffa")==="true":!1,Ze=(r,e)=>{Y()&&console.log(`🎯 [DEBUG MARAFFA] Maraffa forzata per giocatore ${r+1} con seme ${e}`)},z=(r,e,n=U.Coins)=>{const o=[...r],t=[...o[e]],i=[{suit:n,rank:R.Ace,id:`${n}_${R.Ace}_forced`},{suit:n,rank:R.Two,id:`${n}_${R.Two}_forced`},{suit:n,rank:R.Three,id:`${n}_${R.Three}_forced`}],s=[...t.filter(u=>u.suit!==n).slice(0,7),...i];for(;s.length<10;){const u=[U.Cups,U.Swords,U.Clubs].filter(c=>c!==n),g=u[Math.floor(Math.random()*u.length)],l=[R.Four,R.Five,R.Six,R.Seven][Math.floor(Math.random()*4)];s.push({suit:g,rank:l,id:`${g}_${l}_filler_${s.length}`})}return o[e]=s.slice(0,10),Ze(e,n),o},Oe=r=>{let e=[...r];if(de()&&(e=z(e,0,U.Coins)),ge()){const n=Math.floor(Math.random()*3)+1,t=[U.Cups,U.Swords,U.Clubs][n-1];e=z(e,n,t)}return de()&&ge()&&(console.log("🎯 [DEBUG MARAFFA] Modalità TUTTI HANNO MARAFFA attiva - semi diversi per ogni giocatore"),e=z(e,1,U.Cups),e=z(e,2,U.Swords),e=z(e,3,U.Clubs)),e},He=(r,e,n)=>{if(e.currentTrick.length>0||e.trickNumber===1||e.trickNumber>5)return null;const o=n.suit,t=r.hand.filter(l=>l.suit===o),i=e.trumpSuit?r.hand.filter(l=>l.suit===e.trumpSuit):[];if(t.length===1&&i.length>0)return"volo";const a=t.some(l=>l.rank===R.Two),s=t.some(l=>l.rank===R.Three),u=t.some(l=>l.rank===R.Ace);return a&&n.suit===o&&n.rank!==R.Two&&!s&&!u&&t.length>=2?(console.log(`[BUSSO] 🎯 Gioco ${n.rank} di ${n.suit} - ho il 2 dello stesso seme!`),"busso"):t.length===2?"striscio":null},ze=(r,e,n,o)=>{const t=He(r,e,n);if(!t)return null;const i={easy:.4,medium:.7,hard:.9};if(Math.random()>i[o])return null;switch(t){case"busso":return _e(r,e,n)?"busso":null;case"volo":return Math.random()<.8?"volo":null;case"striscio":return Math.random()<.6?"striscio":null;default:return null}},_e=(r,e,n)=>{if(!r.hand.filter(u=>u.suit===n.suit).some(u=>u.rank===R.Two)||e.trickNumber>5)return!1;const i=e.players.findIndex(u=>u.team===r.team&&u.id!==r.id);if(i===-1)return!1;const a=e.currentPlayer;return(i-a+4)%4<=2},Ke=(r,e)=>{if(console.log(`[STRATEGIC ANNOUNCEMENTS] 📢 ${e.players[r.playerIndex].name} dichiara: ${r.type.toUpperCase()}`),typeof window<"u"&&window.aiMemory){const n=window.aiMemory;n.strategicAnnouncements||(n.strategicAnnouncements=[]),n.strategicAnnouncements.push({...r,timestamp:Date.now()});const o=e.trickNumber;n.strategicAnnouncements=n.strategicAnnouncements.filter(t=>o-t.trickNumber<=3)}},Je=(r,e,n)=>{var o;if(typeof window<"u"&&((o=window.aiMemory)!=null&&o.strategicAnnouncements)){const t=window.aiMemory,i=r.players[e],a=t.strategicAnnouncements.find(s=>s.type==="busso"&&s.trickNumber===r.trickNumber&&r.players[s.playerIndex].team===i.team&&s.playerIndex!==e);a&&(console.log(`[BUSSO TRACKING] 🎯 ${i.name} ha preso dopo busso del compagno (${a.suit}). Dovrà tornare in quel seme!`),t.bussoObligations||(t.bussoObligations=[]),t.bussoObligations.push({playerIndex:e,suit:a.suit,trickNumber:r.trickNumber,mustReturnInNextTrick:!0,timestamp:Date.now()}))}},je=(r,e)=>{if(typeof window>"u"||!window.aiMemory)return[];const n=window.aiMemory;if(!n.strategicAnnouncements)return[];const o=r.trickNumber;return n.strategicAnnouncements.filter(t=>{if(o-t.trickNumber>2)return!1;const i=r.players[t.playerIndex],a=r.players[e];return i.team===a.team?!0:t.type==="volo"})},qe=(r,e,n)=>{const o=je(e,n),t=e.players[n],i=o.filter(s=>e.players[s.playerIndex].team===t.team&&s.playerIndex!==n);if(i.length===0)return{recommendedCards:r,strategy:"Nessuna dichiarazione del compagno"};const a=i[i.length-1];switch(a.type){case"busso":return Ye(r,e,a);case"striscio":return Qe(r,e,a);case"volo":return Xe(r,e,a);default:return{recommendedCards:r,strategy:"Dichiarazione non riconosciuta"}}},Ye=(r,e,n)=>{const o=n.suit,t=r.filter(s=>s.suit===o);if(t.length>0){const u=t.sort((g,l)=>{const c=d=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[d.rank]||0;return c(l)-c(g)})[0];return{recommendedCards:[u],strategy:`BUSSO: Prendo con ${u.rank} di ${o} (carta più forte del seme)`}}const i=r.filter(s=>s.suit===e.trumpSuit);if(i.length>0){const s=i.sort((u,g)=>{const l=c=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[c.rank]||0;return l(u)-l(g)});return{recommendedCards:[s[0]],strategy:`BUSSO: Prendo con briscola ${s[0].rank} (non ho carte del seme ${o})`}}const a=r.filter(s=>s.suit===e.trumpSuit?!0:s.suit===o?s.rank===R.Three||s.rank===R.Two||s.rank===R.Ace:!1);return a.length>0?{recommendedCards:a,strategy:`BUSSO: Prendo la mano per collaborare con il compagno (seme: ${o})`}:{recommendedCards:r,strategy:"BUSSO: Non posso prendere la mano"}},Qe=(r,e,n)=>({recommendedCards:r,strategy:`STRISCIO: Il compagno ha ancora carte in ${n.suit}`}),Xe=(r,e,n)=>{const o=n.suit,t=r.filter(i=>i.suit===o);if(t.length>0&&e.leadSuit===o){const i=t.filter(a=>a.rank===R.Ace||a.rank===R.King||a.rank===R.Horse||a.rank===R.Jack);if(i.length>0)return{recommendedCards:i,strategy:`VOLO: Do punti al compagno che tagliarà (seme: ${o})`}}return{recommendedCards:r,strategy:`VOLO: Il compagno può tagliare ${o}`}},Se=r=>{const e=[U.Coins,U.Cups,U.Swords,U.Clubs];for(const n of e)if(Te(r,n))return n;return null},Q=r=>{if(r.gamePhase!=="selectTrump")return{hasMaraffa:!1,maraffaSuit:null,playerIndex:-1};const e=r.players[r.currentPlayer],n=Se(e.hand);return{hasMaraffa:n!==null,maraffaSuit:n,playerIndex:r.currentPlayer}},er=r=>{const e=Q(r);return!e.hasMaraffa||!e.maraffaSuit?r:X(r,e.maraffaSuit)},Ie=(r=31)=>{const e=he(ae()),n=[{id:0,name:"Tu",hand:[],team:0,position:"south"},{id:1,name:"Avversario 1",hand:[],team:1,position:"east"},{id:2,name:"Compagno",hand:[],team:0,position:"north"},{id:3,name:"Avversario 2",hand:[],team:1,position:"west"}];let o=Ce(e,4,10);o=Oe(o),n.forEach((l,c)=>{l.hand=o[c]});const t=[{id:0,players:[n[0],n[2]],score:0,tricksWon:[],currentRoundPoints:0,currentRoundFigures:0},{id:1,players:[n[1],n[3]],score:0,tricksWon:[],currentRoundPoints:0,currentRoundFigures:0}],i=n.findIndex(l=>l.hand.some(c=>c.suit===U.Coins&&c.rank===R.Four)),a=i!==-1?i:0,u=Q({players:n,currentPlayer:a}),g={deck:e,players:n,teams:t,currentPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:i,roundScore:[0,0],gameScore:[0,0],lastTrickWinner:null,announcedAction:null,message:"Il giocatore con il 4 di Denari deve scegliere la briscola",leadPlayer:a,currentRoundScoreHistory:{team0:[],team1:[]},victoryPoints:r,lastTrumpSelector:null,maraffeMade:[0,0],maxScoreDifference:0,automaticMaraffa:u.hasMaraffa?u:null};return u.hasMaraffa&&u.maraffaSuit&&a!==0?X(g,u.maraffaSuit):g},X=(r,e)=>{if(r.gamePhase!=="selectTrump")return r;const n=r.players[r.currentPlayer],o=[...r.teams],t=[...r.maraffeMade];let i=!1;return te(n.hand,e)?(i=!0,console.log(`� MARAFFA RILEVATA! Team ${n.team} ha A+2+3 di ${e}. I punti verranno assegnati quando l'Asso sarà giocato come prima carta.`)):console.log(`❌ NESSUNA MARAFFA: Team ${n.team} non ha A+2+3 di ${e}`),{...r,trumpSuit:e,gamePhase:"play",teams:o,maraffeMade:t,automaticMaraffa:null,message:`La briscola è ${e===U.Coins?"Denari":e===U.Cups?"Coppe":e===U.Swords?"Spade":"Bastoni"}. ${r.players[r.currentPlayer].name} inizia.${i?" Maraffa rilevata! I punti saranno assegnati quando l'Asso verrà giocato.":""}`,leadPlayer:r.currentPlayer,lastTrumpSelector:r.currentPlayer}},ke=(r,e)=>{if(r.gamePhase!=="play"||r.currentTrick.length!==0)return r;const n=r.players[r.currentPlayer];return console.log(`[STRATEGIC ANNOUNCEMENT] ${n.name} dichiara: ${e.toUpperCase()}`),{...r,announcedAction:e,message:`${n.name} dichiara: "${e.toUpperCase()}"`}},ue=r=>{const e=r.filter(i=>i.rank===R.Ace).length,n=r.filter(i=>[R.Three,R.Two,R.King,R.Horse,R.Jack].includes(i.rank)).length,o=e,t=n;return(o>0||t>0)&&console.log(`    🧮 Calcolo: ${e} assi = ${o} punti, ${n} figure`),{acePoints:o,figureCount:t,totalPoints:o+Math.floor(t/3)}},rr=(r,e)=>{const n=r.players[e].team,o=[...r.teams];if(r.currentTrick.length!==4)return console.warn(`⚠️ ERRORE: Tentativo di calcolare punteggio con trick incompleto (${r.currentTrick.length} carte)`),r;const t=ue(r.currentTrick);console.log(`🎯 PRESA ${r.trickNumber}:`),console.log("   Carte giocate:",r.currentTrick.map(a=>`${a.rank} di ${a.suit}`)),console.log(`   Punti da assi: ${t.acePoints}`),console.log(`   Figure trovate: ${t.figureCount}`),console.log(`   Team vincitore: ${n}`);const i={team0:[...r.currentRoundScoreHistory.team0],team1:[...r.currentRoundScoreHistory.team1]};if(n===0){const a=o[0].currentRoundPoints,s=o[0].currentRoundFigures;o[0].currentRoundPoints+=t.acePoints;const u=s+t.figureCount,g=Math.floor(u/3);o[0].currentRoundPoints+=g,o[0].currentRoundFigures=u%3,console.log(`   Team 0: ${a} + ${t.acePoints} (assi) + ${g} (da ${u} figure) = ${o[0].currentRoundPoints}`),console.log(`   Team 0: Figure rimanenti: ${o[0].currentRoundFigures}`),i.team0.push(t.acePoints+g)}else{const a=o[1].currentRoundPoints,s=o[1].currentRoundFigures;o[1].currentRoundPoints+=t.acePoints;const u=s+t.figureCount,g=Math.floor(u/3);o[1].currentRoundPoints+=g,o[1].currentRoundFigures=u%3,console.log(`   Team 1: ${a} + ${t.acePoints} (assi) + ${g} (da ${u} figure) = ${o[1].currentRoundPoints}`),console.log(`   Team 1: Figure rimanenti: ${o[1].currentRoundFigures}`),i.team1.push(t.acePoints+g)}if(o[n].tricksWon.push(...r.currentTrick),r.trickNumber===10){const a=o[n].currentRoundPoints;o[n].currentRoundPoints+=1,console.log(`🏆 ULTIMA PRESA - Team ${n}: ${a} + 1 (bonus) = ${o[n].currentRoundPoints}`),n===0?i.team0.push(1):i.team1.push(1),console.log(`📊 FINE MANO - Totali: Team 0 = ${o[0].currentRoundPoints}, Team 1 = ${o[1].currentRoundPoints}`),console.log(`📊 SOMMA TOTALE: ${o[0].currentRoundPoints+o[1].currentRoundPoints} punti`),console.log(`📊 Figure rimaste: Team 0 = ${o[0].currentRoundFigures}, Team 1 = ${o[1].currentRoundFigures}`)}return{...r,teams:o,currentRoundScoreHistory:i}},oe=(r,e)=>{if(r.gamePhase!=="play")return r;const n=r.players[r.currentPlayer],o=n.hand.findIndex(u=>u.id===e);if(o===-1)return r;const t=n.hand[o];if(r.trumpSuit&&r.trickNumber===1&&r.currentTrick.length===0&&te(n.hand,r.trumpSuit)&&r.lastTrumpSelector===r.currentPlayer&&(t.suit!==r.trumpSuit||t.rank!=="A"))return{...r,message:"🎯 MARAFFA! Devi giocare l'Asso della briscola come prima carta."};if(r.currentTrick.length===0){const u=[...n.hand];u.splice(o,1);const g=[...r.players];g[r.currentPlayer]={...n,hand:u};let l=0;const c=[...r.maraffeMade],d=[...r.teams];if(r.trumpSuit&&r.trickNumber===1&&r.currentTrick.length===0&&t.suit===r.trumpSuit&&t.rank==="A"&&r.lastTrumpSelector===r.currentPlayer&&te(n.hand,r.trumpSuit)){const C=n.hand.some(k=>k.suit===r.trumpSuit&&k.rank==="A"),P=n.hand.some(k=>k.suit===r.trumpSuit&&k.rank==="2"),O=n.hand.some(k=>k.suit===r.trumpSuit&&k.rank==="3");C&&P&&O?(l=3,c[n.team]+=1,d[n.team].currentRoundPoints+=l,console.log(`🎯🎯🎯 MARAFFA COMPLETATA! ${n.name} (Team ${n.team}) ha giocato l'Asso di briscola come prima carta. +${l} punti bonus! Round points aggiornati: ${d[n.team].currentRoundPoints}`)):console.log(`❌ MARAFFA FALSA: ${n.name} non ha tutte le carte della Maraffa!`)}const m=[r.roundScore[0]+(n.team===0?l:0),r.roundScore[1]+(n.team===1?l:0)],h=Math.abs(m[0]-m[1]),f=Math.max(r.maxScoreDifference,h);return{...r,players:g,teams:d,currentTrick:[t],leadSuit:t.suit,leadPlayer:r.currentPlayer,currentPlayer:(r.currentPlayer+1)%4,roundScore:m,maraffeMade:c,maxScoreDifference:f,announcedAction:null,message:l>0?`${n.name} ha giocato ${t.displayName} e ottenuto il bonus Maraffa (+3 punti)!`:`${n.name} ha giocato ${t.displayName}`,maraffaCompleted:l>0&&r.trickNumber===1?{playerIndex:r.currentPlayer,team:n.team,bonus:l}:void 0}}if(r.leadSuit&&t.suit!==r.leadSuit&&n.hand.some(u=>u.suit===r.leadSuit))return{...r,message:`Devi giocare una carta di ${r.leadSuit===U.Coins?"Denari":r.leadSuit===U.Cups?"Coppe":r.leadSuit===U.Swords?"Spade":"Bastoni"} se ne hai una.`};const i=[...n.hand];i.splice(o,1);const a=[...r.players];a[r.currentPlayer]={...n,hand:i};const s=[...r.currentTrick,t];if(s.length===4){const u=xe(s,r.leadSuit,r.trumpSuit),g=s.findIndex(f=>f.id===u.id),l=(r.leadPlayer+g)%4,c=[...r.teams];Je(r,l);const d=rr({...r,players:a,teams:c,currentTrick:s},l),m=d.teams[0].currentRoundPoints,h=d.teams[1].currentRoundPoints;if(r.trickNumber===10){const f=[...d.teams];if(console.log("🔧 FINE MANO: Conversione figure rimanenti in punti..."),f[0].currentRoundFigures>0){const M=Math.floor(f[0].currentRoundFigures/3);M>0&&(console.log(`   Team 0: ${f[0].currentRoundFigures} figure rimanenti = ${M} punti extra`),f[0].currentRoundPoints+=M,f[0].currentRoundFigures=f[0].currentRoundFigures%3)}if(f[1].currentRoundFigures>0){const M=Math.floor(f[1].currentRoundFigures/3);M>0&&(console.log(`   Team 1: ${f[1].currentRoundFigures} figure rimanenti = ${M} punti extra`),f[1].currentRoundPoints+=M,f[1].currentRoundFigures=f[1].currentRoundFigures%3)}const C=f[0].currentRoundPoints,P=f[1].currentRoundPoints;console.log(`📊 PUNTEGGI FINALI CORRETTI: Team 0 = ${C}, Team 1 = ${P}`),console.log(`📊 SOMMA TOTALE CORRETTA: ${C+P} punti`);const O=[r.gameScore[0]+C,r.gameScore[1]+P],k=(O[0]>=r.victoryPoints||O[1]>=r.victoryPoints)&&O[0]!==O[1];return{...d,teams:f,currentTrick:[],leadSuit:null,currentPlayer:l,leadPlayer:l,trickNumber:1,gamePhase:k?"gameOver":"roundOver",lastTrickWinner:l,roundScore:[C,P],gameScore:O,message:k?`Gioco finito! ${O[0]>O[1]?"Squadra 1":"Squadra 2"} ha vinto!`:O[0]>=r.victoryPoints&&O[1]>=r.victoryPoints&&O[0]===O[1]?`Entrambe le squadre hanno raggiunto ${r.victoryPoints} punti ma sono in pareggio (${O[0]}-${O[1]}). La partita continua fino al primo vincitore!`:`${r.players[l].name} ha vinto l'ultima presa. Punteggio mano: ${C}-${P}`}}return{...d,currentTrick:[],leadSuit:null,currentPlayer:l,leadPlayer:l,trickNumber:r.trickNumber+1,lastTrickWinner:l,message:`${r.players[l].name} ha vinto la presa. Punteggio attuale: Squadra 1 (${m.toFixed(1)}) - Squadra 2 (${h.toFixed(1)})`}}return{...r,players:a,currentTrick:s,currentPlayer:(r.currentPlayer+1)%4,message:`${n.name} ha giocato ${t.displayName}`}},nr=r=>{if(r.gamePhase!=="scoring"&&r.gamePhase!=="roundOver")return r;const e=he(ae()),n=[...r.players];let o=Ce(e,4,10);o=Oe(o),n.forEach((g,l)=>{g.hand=o[l]});const t=[{...r.teams[0],tricksWon:[],currentRoundPoints:0,currentRoundFigures:0},{...r.teams[1],tricksWon:[],currentRoundPoints:0,currentRoundFigures:0}],i=n.findIndex(g=>g.hand.some(l=>l.suit===U.Coins&&l.rank===R.Four));let a=0;r.lastTrumpSelector===null?a=i!==-1?i:0:a=(r.lastTrumpSelector+1)%4;const s={...r,players:n,currentPlayer:a},u=Q(s);if(u.hasMaraffa&&u.maraffaSuit&&a!==0){const g={...r,deck:e,players:n,teams:t,currentPlayer:a,leadPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:i,roundScore:[0,0],lastTrickWinner:null,announcedAction:null,currentRoundScoreHistory:{team0:[],team1:[]},automaticMaraffa:u};return X(g,u.maraffaSuit)}return{...r,deck:e,players:n,teams:t,currentPlayer:a,leadPlayer:a,trumpSuit:null,leadSuit:null,currentTrick:[],trickNumber:1,gamePhase:"selectTrump",playerWithFourOfCoins:i,roundScore:[0,0],lastTrickWinner:null,announcedAction:null,currentRoundScoreHistory:{team0:[],team1:[]},automaticMaraffa:u.hasMaraffa?u:null,message:u.hasMaraffa&&a===0?`${n[a].name} ha la Maraffa! Puoi scegliere il seme per la briscola.`:r.lastTrumpSelector===null?`${n[a].name} ha il 4 di Denari e deve scegliere la briscola`:`${n[a].name} deve scegliere la briscola`}},tr=(r,e)=>{if(r.gamePhase!=="play")return r;const n=r.teams[e],o=r.gameScore[e],t=ue(n.tricksWon),i=t.acePoints+Math.floor(t.figureCount/3);return o+i>=r.victoryPoints?(console.log("🎯 gameLogic: Partita finita - vittoria squadra",e+1),{...r,gamePhase:"gameOver",gameScore:[e===0?o+i:r.gameScore[0],e===1?o+i:r.gameScore[1]],message:`Squadra ${e+1} ha dichiarato vittoria e ha raggiunto i punti necessari.`}):(console.log("🎯 gameLogic: Partita finita - dichiarazione fallita squadra",e+1),{...r,gamePhase:"gameOver",roundScore:e===0?[0,11]:[11,0],gameScore:[r.gameScore[0]+(e===0?0:11),r.gameScore[1]+(e===1?0:11)],message:`Squadra ${e+1} ha dichiarato vittoria ma non ha abbastanza punti. Squadra ${e===0?2:1} vince 11-0.`})},or=(r=31)=>Ie(r),wr=Object.freeze(Object.defineProperty({__proto__:null,announceAction:ke,applyAutomaticMaraffa:er,calculateScore:ue,checkForAutomaticMaraffa:Q,declareGameWin:tr,findAutomaticMaraffa:Se,initializeGameState:Ie,playCard:oe,selectTrump:X,startNewGame:or,startNewRound:nr},Symbol.toStringTag,{value:"Module"}));var G=(r=>(r[r.EASY=0]="EASY",r[r.MEDIUM=1]="MEDIUM",r[r.HARD=2]="HARD",r))(G||{});const ie=()=>ae(),W=r=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[r.rank||""]||0,B=r=>{switch(r.rank){case R.Ace:return 1;case R.Three:case R.Two:case R.King:case R.Horse:case R.Jack:return .3;default:return 0}},_=r=>r.rank===R.Ace||r.rank===R.King||r.rank===R.Horse||r.rank===R.Jack,K=r=>r.filter(_),me=(r,e,n)=>{const o=n.trumpSuit,t=n.leadSuit;if(r.suit===o&&r.rank===R.Three)return!0;if(r.suit===o){const i=[];r.rank!==R.Three&&i.push(R.Three),r.rank!==R.Two&&r.rank!==R.Three&&i.push(R.Two),r.rank!==R.Ace&&r.rank!==R.Two&&r.rank!==R.Three&&i.push(R.Ace);const a=e.playedBySuit[o]||[];return i.every(u=>a.some(g=>g.rank===u))}if(t&&r.suit===t){const i=[],a=W(r),s=[R.Three,R.Two,R.Ace,R.King,R.Horse,R.Jack,"7","6","5","4"];for(const d of s)W({rank:d,suit:r.suit})>a&&i.push(d);const u=e.playedBySuit[t]||[],g=i.every(d=>u.some(m=>m.rank===d)),c=(e.playedBySuit[o]||[]).length>=10;return g&&c}return!1},J=r=>({3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1})[r.rank]||0,j=(r,e,n,o)=>{if(!e||e.length===0)return!0;const t=o&&r.suit===o,i=e.filter(a=>o&&a.suit===o);if(t){if(i.length===0)return!0;{const a=i.reduce((s,u)=>W(u)>W(s)?u:s);return W(r)>W(a)}}else{if(i.length>0||!n||r.suit!==n)return!1;const a=e.filter(u=>n&&u.suit===n);if(a.length===0)return!0;const s=a.reduce((u,g)=>W(g)>W(u)?g:u);return W(r)>W(s)}},ir=(r,e,n,o)=>n&&r.suit===n?sr(r,e):ar(r,e,n),sr=(r,e,n)=>{const o=e.trumpsRemaining,t=W(r);return o.filter(a=>W(a)>t).length===0},ar=(r,e,n)=>{const o=e.remainingCards.filter(a=>a.suit===r.suit),t=W(r);return o.filter(a=>W(a)>t).length>0?!1:n&&r.suit!==n?e.trumpsRemaining.length===0:!0},fe=(r,e,n,o)=>{if(r.length===0)return 0;let t=0,i=r[0];for(let a=1;a<r.length;a++){const s=r[a];ur(s,i,e,n)&&(t=a,i=s)}return t},ur=(r,e,n,o)=>o&&r.suit===o&&e.suit===o?W(r)>W(e):o&&r.suit===o&&e.suit!==o?!0:o&&r.suit!==o&&e.suit===o?!1:n&&r.suit===n&&e.suit===n?W(r)>W(e):n&&r.suit===n&&e.suit!==n?!0:(n&&r.suit!==n&&e.suit===n,!1),cr=(r,e,n)=>{if(!e)return!1;const o=["3","2","A","K","H","J","7","6","5","4"],t=W(r),i=o.filter(u=>{const g={rank:u,suit:r.suit};return W(g)>t}),a=e.playedBySuit[r.suit]||[];return i.every(u=>a.some(g=>g.rank===u))?(console.log(`[DOMINANZA] 🏆 ${r.rank} di ${r.suit} è DOMINANTE! Tutte le carte più forti sono state giocate`),console.log(`[DOMINANZA] Carte più forti giocate: ${i.join(", ")}`),!0):!1},ye=r=>r.trumpSuit?[...r.teams[0].tricksWon,...r.teams[1].tricksWon,...r.currentTrick||[]].filter(n=>n&&n.suit===r.trumpSuit).length:0,lr=(r,e,n)=>{if(!e.trumpSuit)return{shouldPlayTrump:!1,recommendedCard:null,reason:"Nessuna briscola selezionata"};const o=r.filter(s=>s.suit===e.trumpSuit);if(o.length<4)return{shouldPlayTrump:!1,recommendedCard:null,reason:`Solo ${o.length} briscole, strategia non applicabile`};const t=ye(e),a=10-t-o.length;if(console.log(`[STRATEGIA MOLTE BRISCOLE] 🎯 Ho ${o.length} briscole, giocate: ${t}, rimaste agli avversari: ~${a}`),a>2){const s=o.filter(g=>!["3","2","A"].includes(g.rank));let u;if(s.length>0)u=s[0];else{const g=new CardEvaluator;u=o.sort((c,d)=>g.getCardOrder(c)-g.getCardOrder(d))[0]}return{shouldPlayTrump:!0,recommendedCard:u,reason:`Strategia molte briscole: gioco ${u.rank} per far finire le briscole agli avversari (~${a} rimaste)`}}return{shouldPlayTrump:!1,recommendedCard:null,reason:`Avversari hanno poche briscole (~${a}), non serve continuare la strategia`}},dr=(r,e,n)=>{if(!e.trumpSuit)return{shouldConserve:!1,cardToAvoid:null,reason:"Nessuna briscola selezionata"};const o=r.filter(m=>m.suit===e.trumpSuit),t=e.trickNumber||1,i=t>=8;if(t===10)return{shouldConserve:!1,cardToAvoid:null,reason:"È l'ultimo turno, gioca tutto"};if(o.filter(m=>["3","2","A"].includes(m.rank)).length===0)return{shouldConserve:!1,cardToAvoid:null,reason:"Non ho briscole alte da conservare"};ye(e);const u=[...e.teams[0].tricksWon,...e.teams[1].tricksWon,...e.currentTrick||[]],g=u.some(m=>m&&m.suit===e.trumpSuit&&m.rank==="3"),l=u.some(m=>m&&m.suit===e.trumpSuit&&m.rank==="2"),c=u.some(m=>m&&m.suit===e.trumpSuit&&m.rank==="A");let d=null;return!g&&o.some(m=>m.rank==="3")?d=o.find(m=>m.rank==="3")||null:!l&&o.some(m=>m.rank==="2")?d=o.find(m=>m.rank==="2")||null:!c&&o.some(m=>m.rank==="A")&&(d=o.find(m=>m.rank==="A")||null),d?i?{shouldConserve:!0,cardToAvoid:d,reason:`Conservo ${d.rank} di briscola per l'ultimo turno (turno ${t}/10)`}:{shouldConserve:!1,cardToAvoid:null,reason:"Troppo presto per conservare carte forti"}:{shouldConserve:!1,cardToAvoid:null,reason:"Tutte le briscole forti sono già uscite"}},gr=(r,e)=>{const n=[],o={};return r.forEach(t=>{o[t.suit]||(o[t.suit]=[]),o[t.suit].push(t)}),Object.keys(o).forEach(t=>{const i=o[t],a=i.some(u=>u.rank==="2"),s=i.some(u=>u.rank==="3");a&&s?e.playedCards.some(g=>g.suit===t&&g.rank==="3")?(console.log(`[PRIORITÀ 3 SU 2] ✅ Il 3 di ${t} è già uscito - ora il 2 è sicuro`),n.push(...i)):(console.log(`[PRIORITÀ 3 SU 2] 🎯 Ho sia 2 che 3 di ${t}, il 3 non è uscito - preferisco il 3`),i.forEach(g=>{g.rank!=="2"&&n.push(g)})):n.push(...i)}),n},mr=(r,e,n)=>{if(!((e.currentTrick||[]).length===0))return{shouldPlaySuit:!1,recommendedCard:null,reason:"Non sono primo del turno"};if(typeof window<"u"&&window.aiMemory){const i=window.aiMemory,a=e.players[n];if(i.bussoObligations){const s=i.bussoObligations.find(u=>u.playerIndex===n&&u.mustReturnInNextTrick&&u.trickNumber===e.trickNumber-1);if(s){const u=s.suit,g=r.filter(l=>l.suit===u);if(g.length>0){const l=g.filter(d=>["A","3","2"].includes(d.rank)),c=l.length>0?l[0]:g[0];return i.bussoObligations=i.bussoObligations.filter(d=>d!==s),{shouldPlaySuit:!0,recommendedCard:c,reason:`OBBLIGO BUSSO: Devo tornare in ${u} con ${c.rank} dopo aver preso`}}else return i.bussoObligations=i.bussoObligations.filter(l=>l!==s),{shouldPlaySuit:!1,recommendedCard:null,reason:`ERRORE OBBLIGO: Dovrei tornare in ${u} ma non ho più carte di quel seme`}}}if(i.strategicAnnouncements){const u=i.strategicAnnouncements.filter(g=>g.type==="busso"&&e.trickNumber-g.trickNumber<=2&&e.players[g.playerIndex].team===a.team&&g.playerIndex!==n).sort((g,l)=>l.trickNumber-g.trickNumber)[0];if(u){const g=u.suit,l=r.filter(c=>c.suit===g);if(l.length>0){const c=l.filter(m=>["A","3","2"].includes(m.rank)),d=c.length>0?c[0]:l[0];return{shouldPlaySuit:!0,recommendedCard:d,reason:`Seguo busso del compagno: cerco di prendere con ${d.rank} di ${g}`}}else return{shouldPlaySuit:!1,recommendedCard:null,reason:`Vorrei seguire busso del compagno (${g}) ma non ho carte di quel seme`}}}}return{shouldPlaySuit:!1,recommendedCard:null,reason:"Nessun busso recente del compagno da seguire"}},Re=r=>{const e=ie(),n=[],o={},t={};[U.Coins,U.Cups,U.Swords,U.Clubs].forEach(c=>{o[c]=[]});for(let c=0;c<4;c++)t[c]=[];[...r.teams[0].tricksWon,...r.teams[1].tricksWon].forEach(c=>{c&&(n.push(c),c.suit&&o[c.suit].push(c))}),r.currentTrick&&r.currentTrick.length>0&&r.currentTrick.forEach((c,d)=>{if(c){n.push(c),c.suit&&o[c.suit].push(c);const m=((r.leadPlayer??0)+d)%4;t[m].push(c)}}),console.log(`[MEMORY ANALYSIS] 📊 Carte giocate totali: ${n.length}`),console.log("[MEMORY ANALYSIS] 📊 Dettaglio per seme:",Object.keys(o).map(c=>`${c}: ${o[c].length}`).join(", ")),r.teams&&r.teams.forEach(c=>{c.tricksWon&&Array.isArray(c.tricksWon)&&c.tricksWon.forEach(d=>{Array.isArray(d)&&d.forEach((m,h)=>{if(m&&m.suit&&m.rank){n.push(m),o[m.suit.toString()].push(m);const C=((r.leadPlayer??0)+h)%4;t[C].push(m)}})})}),r.currentTrick&&Array.isArray(r.currentTrick)&&r.currentTrick.forEach((c,d)=>{if(c&&c.suit&&c.rank){n.push(c),o[c.suit.toString()].push(c);const m=((r.leadPlayer??0)+d)%4;t[m].push(c)}});const a=e.filter(c=>!n.some(d=>c.suit===d.suit&&c.rank===d.rank)),s={};[U.Coins,U.Cups,U.Swords,U.Clubs].forEach(c=>{s[c]=a.filter(d=>d.suit===c).length});const u=a.filter(c=>c.suit===r.trumpSuit),g={};return[U.Coins,U.Cups,U.Swords,U.Clubs].forEach(c=>{g[c]=a.filter(d=>d.suit===c&&["A","3","2","K","H","J"].includes(d.rank))}),{playedCards:n,playedBySuit:o,playedByPlayer:t,remainingCards:a,suitDistribution:s,trumpsRemaining:u,highCardsRemaining:g}},se=r=>{const e={playedCards:[],playedBySuit:{},playedByPlayer:{},remainingCards:ie(),suitDistribution:{},trumpsRemaining:[],highCardsRemaining:{}};[U.Coins,U.Cups,U.Swords,U.Clubs].forEach(n=>{e.playedBySuit[n]=[],e.suitDistribution[n]=10,e.highCardsRemaining[n]=ie().filter(o=>o.suit===n&&["A","3","2","K","H","J"].includes(o.rank))});for(let n=0;n<4;n++)e.playedByPlayer[n]=[];return r.currentTrick&&r.currentTrick.length>0&&r.currentTrick.forEach((n,o)=>{e.playedCards.push(n),n.suit&&e.playedBySuit[n.suit.toString()].push(n);const t=((r.leadPlayer??0)+o)%4;e.playedByPlayer[t].push(n)}),e.trumpsRemaining=e.remainingCards.filter(n=>n.suit===r.trumpSuit),e};let x=class{getCardValue(e){switch(e.rank){case R.Ace:return 1;case R.Three:case R.Two:case R.King:case R.Horse:case R.Jack:return .3;default:return 0}}getCardOrder(e){return{3:10,2:9,A:8,K:7,H:6,J:5,7:4,6:3,5:2,4:1}[e.rank]||0}getCardStrengthScore(e,n=!1){const o=this.getCardOrder(e),t=this.getCardValue(e),i=n?5:0;return o+t*2+i}isStrategicCard(e){return e.rank===R.Two}isThreeForOpening(e,n){return e.rank===R.Three&&e.suit!==n}isGoodForTeammate(e){return e.rank===R.Ace||e.rank===R.King||e.rank===R.Horse||e.rank===R.Jack}canWinCurrentTrick(e,n,o,t){if(!n||n.length===0)return!0;const i=e.suit===t;for(const a of n){const s=a.suit===t;if(!(i&&!s)){if(!i&&s)return!1;if(i&&s){if(this.getCardOrder(e)<=this.getCardOrder(a))return!1}else if(e.suit===o&&a.suit===o){if(this.getCardOrder(e)<=this.getCardOrder(a))return!1}else if(e.suit!==o&&a.suit===o)return!1}}return!0}isObviousWinSituation(e,n,o,t,i=0){if(!this.canWinCurrentTrick(e,n,o,t))return!1;if(e.rank==="3"&&e.suit===o&&e.suit!==t||e.rank==="2"&&e.suit===o&&e.suit!==t&&!n.some(g=>g.rank==="3"&&g.suit===o))return!0;const a=e.suit===t;if(!a&&(e.rank==="3"||e.rank==="2")&&(n.length<=1||i>=1||n.length>=2&&!n.some(l=>l.rank==="A"||l.rank==="3"||l.rank==="2")))return!0;if(a&&(e.rank==="3"||e.rank==="2")&&i>=2){const u=n.filter(l=>l.suit===t);return u.length===0?!0:u.filter(l=>this.getCardOrder(l)>this.getCardOrder(e)).length===0}if(e.suit===t&&(e.rank==="3"||e.rank==="2")){const u=n.filter(l=>l.suit===t);return u.length===0?!0:u.filter(l=>this.getCardOrder(l)>this.getCardOrder(e)).length===0}return!1}findLowestValidCard(e,n,o){return this.findBestDiscardCard(e,n,o)}selectOptimalTrumpSuit(e){const n=this.findMaraffa(e);if(n)return n;const o={};Object.values(U).forEach(i=>o[i]=0),e.forEach(i=>o[i.suit]++);const t=Object.entries(o).find(([i,a])=>a>=4);return t?t[0]:this.chooseSuitWithHighestCards(e)}findMaraffa(e){const n={};Object.values(U).forEach(o=>n[o]=[]),e.forEach(o=>n[o.suit].push(o));for(const[o,t]of Object.entries(n)){const i=t.map(a=>a.rank);if(i.includes(R.Ace)&&i.includes(R.Two)&&i.includes(R.Three))return o}return null}chooseSuitWithHighestCards(e){const n={};return Object.values(U).forEach(o=>n[o]=0),e.forEach(o=>{n[o.suit]+=this.getCardStrengthScore(o)}),Object.entries(n).reduce((o,[t,i])=>i>n[o]?t:o,Object.keys(n)[0])}shouldLoadTrick(e,n,o,t,i,a){if(!(e.length===3))return{shouldLoad:!1,reason:"Non ultimo giocatore",loadingCards:[]};const u=this.analyzeCurrentWinner(e,n,o,t,a),g=a.players[i].team;if(!(u.winnerTeam===g&&u.winnerIndex!==(i-t+4)%4))return{shouldLoad:!1,reason:"Compagno non sta vincendo",loadingCards:[]};const c=e.reduce((k,M)=>k+this.getCardValue(M),0),d=a.trickNumber??1,m=d===10,h=d>=8;if(!(c>=1||h||m||c>=.5))return{shouldLoad:!1,reason:`Trucco non abbastanza prezioso (${c} punti)`,loadingCards:[]};const P=a.players[i].hand,O=this.getLoadingCards(P);return{shouldLoad:!0,reason:`Caricamento trucco: compagno vincente, ${c} punti attuali`,loadingCards:O}}getCooperativePlayStrategy(e,n,o,t,i,a,s){if(n.length===0){if(console.log("[STRATEGIA] 🎯 PRIMO A GIOCARE - Strategia intelligente"),(s.trickNumber??1)<=3){const y=e.filter($=>$.rank==="3"&&$.suit!==t);if(y.length>0){const $=y[0];return console.log(`[STRATEGIA] 🎯 PRIMO (prime mani): Controllo con 3 di ${$.suit}!`),{strategy:"compete",recommendedCard:$,reason:`🎯 CONTROLLO PRIME MANI: 3 di ${$.suit} come primo giocatore per controllare il tavolo`}}}const V=e.filter(y=>this.getCardValue(y)===0&&y.suit!==t&&y.rank!=="2");if(V.length>0){V.sort(($,p)=>this.getCardOrder(p)-this.getCardOrder($));const y=V[0];return{strategy:"compete",recommendedCard:y,reason:`🎯 APERTURA SICURA: ${y.rank} di ${y.suit} - conservo carte di valore per momenti giusti`}}const v=[...e].sort((y,$)=>this.getCardValue(y)-this.getCardValue($)),T=v.filter(y=>!(y.rank==="3"&&y.suit===t)),E=T.length>0?T[0]:v[0];return{strategy:"compete",recommendedCard:E,reason:`🎯 APERTURA ULTIMA RISORSA: ${E.rank} di ${E.suit} - evito 3 di briscola`}}const u=this.selectOptimalCardWithSafetyCheck(e,n,o,t,s,a);if(u)return console.log(`[SAFE ACE PRIORITY] ${u.reason}`),{strategy:u.strategy,recommendedCard:u.recommendedCard,reason:u.reason};if(n.some(N=>N.rank==="A")){const N=e.filter(L=>this.canWinCurrentTrick(L,n,o,t));if(N.length>0){N.sort((V,v)=>{const T=this.isStrategicCard(V),E=this.isStrategicCard(v);if(!T&&E)return-1;if(T&&!E)return 1;if(T&&E){if(V.rank==="2"&&v.rank==="3")return-1;if(V.rank==="3"&&v.rank==="2")return 1}return this.getCardStrengthScore(V)-this.getCardStrengthScore(v)});const L=N[0];return{strategy:"compete",recommendedCard:L,reason:`🔥 ASSO SUL TAVOLO (1 punto)! Prendo con ${L.rank} - 1 punto vale QUALSIASI carta!`}}}const l=n.reduce((N,L)=>N+this.getCardValue(L),0);if(l>=1){const N=e.filter(L=>this.canWinCurrentTrick(L,n,o,t));if(N.length>0)return N.sort((L,V)=>this.getCardStrengthScore(L)-this.getCardStrengthScore(V)),{strategy:"compete",recommendedCard:N[0],reason:`💰 ${l.toFixed(1)} PUNTI SUL TAVOLO - Vale qualsiasi carta per vincere!`}}if(l>=.6){const N=se(s),L=this.shouldUseStrategicCardsForPoints(e,n,o,t,l,N,n.length===3);if(L.shouldUse&&L.recommendedCard)return console.log(`[STRATEGIA] ${L.reason}`),{strategy:"compete",recommendedCard:L.recommendedCard,reason:L.reason};const V=e.filter(v=>this.canWinCurrentTrick(v,n,o,t));if(V.length>0){const v=V.filter(T=>!this.isStrategicCard(T));if(v.length>0)return v.sort((T,E)=>this.getCardStrengthScore(T)-this.getCardStrengthScore(E)),{strategy:"compete",recommendedCard:v[0],reason:`💰 ${l.toFixed(1)} punti - Prendo con ${v[0].rank}, conservo 2 e 3 per situazioni migliori!`}}console.log(`[STRATEGIA] ${L.reason}`)}const c=this.analyzeCurrentWinner(n,o,t,i,s),d=s.players[a].team,m=c.winnerTeam===d,h=c.winnerTeam!==d&&c.winnerTeam!==-1;if(n.length===3){console.log("[STRATEGIA] 🎯 ULTIMO GIOCATORE - Analisi priorità assoluta");const N=e.filter(v=>this.canWinCurrentTrick(v,n,o,t)&&this.getCardValue(v)>0&&!(v.suit===t&&this.isStrategicCard(v)));if(N.length>0){N.sort((T,E)=>{const y=this.getCardValue(T),$=this.getCardValue(E);return y!==$?$-y:this.getCardOrder(T)-this.getCardOrder(E)});const v=N[0];return console.log(`[STRATEGIA] 🔥 ULTIMO GIOCATORE - Prendo con ${v.rank} (${this.getCardValue(v)} punti)`),{strategy:"compete",recommendedCard:v,reason:`🔥 ULTIMO GIOCATORE: Prendo sempre con carte di valore! ${v.rank} = ${this.getCardValue(v)} punti garantiti`}}if(l>0){const v=e.filter(T=>this.canWinCurrentTrick(T,n,o,t)&&T.suit!==t);if(v.length>0)return v.sort((T,E)=>this.getCardStrengthScore(T)-this.getCardStrengthScore(E)),console.log(`[STRATEGIA] 💰 ULTIMO GIOCATORE - Prendo ${l.toFixed(1)} punti con carta non-briscola`),{strategy:"compete",recommendedCard:v[0],reason:`💰 ULTIMO GIOCATORE: ${l.toFixed(1)} punti sul tavolo - Prendo con ${v[0].rank} non-briscola`}}const L=se(s),V=this.shouldUseStrategicCardsForPoints(e,n,o,t,l,L,!0);if(V.shouldUse&&V.recommendedCard)return console.log(`[STRATEGIA] ${V.reason}`),{strategy:"compete",recommendedCard:V.recommendedCard,reason:V.reason};console.log(`[STRATEGIA] ${V.reason}`)}if(m){console.log("[STRATEGIA] 🎯 TEAM VINCENTE - DEVE DARE PUNTI SUBITO!");const N=e.filter(v=>this.getCardValue(v)>0);if(N.length>0){N.sort((T,E)=>{const y=this.getCardValue(T),$=this.getCardValue(E);return T.rank==="A"&&E.rank!=="A"?-1:E.rank==="A"&&T.rank!=="A"?1:y!==$?$-y:this.getCardOrder(E)-this.getCardOrder(T)});const v=N[0];return console.log(`[STRATEGIA] 🔥 TEAM VINCENTE: Gioco SUBITO ${v.rank} (${this.getCardValue(v)} punti) - NON accumulo!`),{strategy:"give_points",recommendedCard:v,reason:`🔥 STRATEGIA CORRETTA: Team vince = gioco SUBITO ${v.rank} (${this.getCardValue(v)} pt)! Non accumulo carte con punti!`}}const L=e.filter(v=>this.getCardValue(v)===0&&!this.isStrategicCard(v));if(L.length>0)return L.sort((v,T)=>this.getCardOrder(v)-this.getCardOrder(T)),{strategy:"give_points",recommendedCard:L[0],reason:`🤝 TEAM vincente: Nessuna carta con punti, scarto sicuro ${L[0].rank}`};const V=e.filter(v=>this.isStrategicCard(v));if(V.length>0){const v=V.filter(E=>!(E.rank==="3"&&E.suit===t)),T=v.length>0?v[0]:V[0];return{strategy:"give_points",recommendedCard:T,reason:`🤝 TEAM vincente: Ultima opzione ${T.rank} - conservo 3 di briscola se possibile`}}return{strategy:"give_points",recommendedCard:e[0],reason:"🤝 TEAM vincente: Fallback"}}if(h){const N=e.filter(V=>this.getCardValue(V)===0);if(N.length>0)return N.sort((V,v)=>this.getCardOrder(V)-this.getCardOrder(v)),{strategy:"discard_low",recommendedCard:N[0],reason:`🤝 COLLABORATIVO: Avversari vincenti, scarto ${N[0].rank} (0 punti)!`};const L=[...e].sort((V,v)=>this.getCardValue(V)-this.getCardValue(v));return{strategy:"discard_low",recommendedCard:L[0],reason:`🤝 COLLABORATIVO: Costretto a dare ${L[0].rank} (${this.getCardValue(L[0])} punti) agli avversari`}}const C=s.trickNumber??1,P=e.filter(N=>this.getCardValue(N)>0),O=P.length>=4,k=C>=4&&C<=6;if(O&&k){console.log(`[STRATEGIA] ⚠️ CONTROLLO ACCUMULO: ${P.length} carte con punti a metà partita`);const N=P.filter(L=>L.rank==="K"||L.rank==="H"||L.rank==="J");if(N.length>=3)return N.sort((L,V)=>this.getCardValue(L)-this.getCardValue(V)),{strategy:"support_passive",recommendedCard:N[0],reason:`⚠️ CONTROLLO ESTREMO: Troppe figure (${N.length}) - gioco ${N[0].rank} per bilanciare mano`}}const M=e.filter(N=>this.canWinCurrentTrick(N,n,o,t));if(M.length>0&&l>=1)return M.sort((N,L)=>this.getCardStrengthScore(N)-this.getCardStrengthScore(L)),{strategy:"compete",recommendedCard:M[0],reason:`🤝 COMPETIZIONE LOGICA: Situazione neutra, prendo ${l.toFixed(1)} punti per il team!`};const w=e.filter(N=>this.getCardValue(N)===0);return w.length>0?{strategy:"support_passive",recommendedCard:w[0],reason:`🤝 COLLABORATIVO: Supporto passivo con ${w[0].rank}`}:{strategy:"support_passive",recommendedCard:e[0],reason:"🤝 COLLABORATIVO: Fallback"}}getDiscardOrderScore(e){return{4:1,5:2,6:3,7:4,J:5,H:6,K:7,2:8,3:9,A:10}[e.rank]||0}findBestDiscardCard(e,n,o){if(!n){const s=e.filter(g=>g.suit!==o);return s.length>0?(s.sort((g,l)=>this.getDiscardOrderScore(g)-this.getDiscardOrderScore(l)),s[0]):[...e].sort((g,l)=>this.getDiscardOrderScore(g)-this.getDiscardOrderScore(l))[0]}const t=e.filter(s=>s.suit===n);if(t.length>0)return t.sort((s,u)=>this.getDiscardOrderScore(s)-this.getDiscardOrderScore(u)),t[0];const i=e.filter(s=>s.suit!==o);return i.length>0?(i.sort((s,u)=>this.getDiscardOrderScore(s)-this.getDiscardOrderScore(u)),i[0]):[...e].sort((s,u)=>this.getDiscardOrderScore(s)-this.getDiscardOrderScore(u))[0]}getLoadingCards(e){const n=e.filter(t=>this.getCardValue(t)>0&&!this.isStrategicCard(t)),o=e.filter(t=>this.isStrategicCard(t));return n.sort((t,i)=>this.getCardValue(i)-this.getCardValue(t)),o.sort((t,i)=>this.getCardValue(i)-this.getCardValue(t)),[...n,...o]}getBestLoadingCard(e,n,o){if(!this.shouldLoadTrick(n,o.leadSuit,o.trumpSuit,o.leadPlayer??0,o.currentPlayer,o).shouldLoad)return null;const i=e.filter(u=>this.getCardValue(u)>0&&!this.isStrategicCard(u));if(i.length>0)return i.sort((u,g)=>this.getCardValue(g)-this.getCardValue(u)),i[0];const a=e.filter(u=>this.getCardValue(u)===0);if(a.length>0)return a[0];const s=e.filter(u=>this.isStrategicCard(u));return s.length>0?(console.warn("[LOADING] ⚠️ Costretto a caricare con carta strategica - situazione subottimale!"),s[0]):null}canPlayAceSafely(e,n,o,t,i,a){if(n.length>0)if(console.log(`[SAFE ACE CHECK] 🚨 Controllo draconiano per asso ${e.suit}`),this.canWinCurrentTrick(e,n,o,t))console.log(`[SAFE ACE CHECK] ✅ ASSO ${e.suit} sicuro perché può vincere`);else{const l=i.players[a].team;let c=0,d=-1,m=!1;for(let P=0;P<n.length;P++){const O=n[P],k=this.getCardOrder(O),M=O.suit===t;M&&!m?(c=P,d=k,m=!0):(M&&m&&k>d||!m&&O.suit===o&&k>d)&&(c=P,d=k)}const h=((i.leadPlayer||0)+c)%4;if(!(i.players[h].team===l))return console.log(`[SAFE ACE CHECK] 🚫 ASSO ${e.suit} NON SICURO - non vince e team non sta vincendo!`),!1;console.log(`[SAFE ACE CHECK] ✅ ASSO ${e.suit} sicuro per supporto team`)}return e.suit===t||!this.canWinCurrentTrick(e,n,o,t)&&n.length>0?!1:4-n.length-1===0||(i.trickNumber??1)===1&&e.suit===o?!0:e.suit===o?!n.some(l=>l.suit===o&&(l.rank==="2"||l.rank==="3")):!1}getSafeAces(e,n,o,t,i,a){return e.filter(s=>s.rank===R.Ace&&this.canPlayAceSafely(s,n,o,t,i,a))}selectOptimalCardWithSafetyCheck(e,n,o,t,i,a){const s=this.getSafeAces(e,n,o,t,i,a);if(s.length>0){console.log(`[SAFE ACES] 🔥 Identificati ${s.length} assi sicuri`);const u=s.filter(c=>this.canWinCurrentTrick(c,n,o,t));if(u.length>0)return{recommendedCard:u[0],reason:`🔥 ASSO SICURO: ${u[0].rank} di ${u[0].suit} può vincere senza rischi`,strategy:"safe_ace_play"};const g=n.length===3,l=n.reduce((c,d)=>c+this.getCardValue(d),0);if(g&&l>0)return{recommendedCard:s[0],reason:`🎯 ASSO SICURO ultimo giocatore: 1 punto garantito + ${l.toFixed(1)} punti sul tavolo`,strategy:"safe_ace_loading"}}return null}analyzeCurrentWinner(e,n,o,t,i){if(e.length===0)return{winnerIndex:-1,winnerTeam:-1,winnerCard:null};let a=0,s=e[0],u=this.calculateCardTrickValue(s,n,o);for(let c=1;c<e.length;c++){const d=e[c],m=this.calculateCardTrickValue(d,n,o);m>u&&(u=m,a=c,s=d)}const g=(t+a)%4,l=i.players[g].team;return{winnerIndex:a,winnerTeam:l,winnerCard:s}}calculateCardTrickValue(e,n,o){return e.suit===o?1e3+this.getCardOrder(e):e.suit===n?this.getCardOrder(e):0}analyzeTeammateCollaborativeStatus(e,n,o,t,i,a){if(e.length===0)return{teammateExists:!1,teammateIsWinning:!1,teammateCanBeBeaten:!1,teammateCard:null,shouldLoadTrick:!1,shouldProtectTeammate:!1,collaborativeAction:"neutral",reason:"Primo a giocare - nessun compagno da analizzare"};const s=a.players[i].team;let u=null,g=-1;for(let O=0;O<e.length;O++){const k=(t+O)%4;if(a.players[k].team===s&&k!==i){u=e[O],g=O;break}}if(!u)return{teammateExists:!1,teammateIsWinning:!1,teammateCanBeBeaten:!1,teammateCard:null,shouldLoadTrick:!1,shouldProtectTeammate:!1,collaborativeAction:"neutral",reason:"Nessun compagno nel trick corrente"};const l=this.analyzeCurrentWinner(e,n,o,t,a),c=l.winnerTeam===s&&l.winnerIndex===g,d=4-e.length-1;let m=!1;c&&d>0&&(m=!this.isCardUnbeatable(u,e,n,o,a));let h,f,C=!1,P=!1;if(c){const O=e.reduce((w,N)=>w+this.getCardValue(N),0),k=e.length===3,M=O>=1;k&&(M||a.trickNumber===10)?(h="support",C=!0,f=`Compagno vincente - valorizzare presa (${O.toFixed(1)} punti)`):m?(h="protect",P=!0,f="Compagno vincente ma vulnerabile - proteggere"):(h="support",f="Compagno sicuramente vincente - supporto passivo")}else this.canWinCurrentTrick(a.players[i].hand[0],e,n,o)?(h="compete",f="Compagno non sta vincendo - posso competere per il team"):(h="neutral",f="Compagno non vincente e non posso aiutare");return{teammateExists:!0,teammateIsWinning:c,teammateCanBeBeaten:m,teammateCard:u,shouldLoadTrick:C,shouldProtectTeammate:P,collaborativeAction:h,reason:f}}isCardUnbeatable(e,n,o,t,i){return e.suit===t&&e.rank==="3"?!0:e.suit===t&&e.rank==="2"?!n.some(s=>s.suit===t&&s.rank==="3"):e.suit===o&&e.rank==="3"?!n.some(s=>s.suit===t):!1}shouldUseStrategicCardsForPoints(e,n,o,t,i,a,s=!1){const u=e.filter(h=>this.isStrategicCard(h)&&this.canWinCurrentTrick(h,n,o,t));if(u.length===0)return{shouldUse:!1,recommendedCard:null,reason:"Nessuna carta strategica può vincere"};if(n.some(h=>h.rank==="A"))return u.sort((h,f)=>h.rank==="2"&&f.rank==="3"?-1:h.rank==="3"&&f.rank==="2"?1:this.getCardStrengthScore(h)-this.getCardStrengthScore(f)),{shouldUse:!0,recommendedCard:u[0],reason:"🔥 ASSO SUL TAVOLO! Vale qualsiasi carta strategica per 1 punto"};const l=u.map(h=>{let f=!1;return a&&a.playedBySuit&&a.playedBySuit[h.suit]&&(f=a.playedBySuit[h.suit].some(C=>C.rank==="A")),{card:h,aceAlreadyPlayed:f,effectiveThreshold:f?.6:Number.MAX_SAFE_INTEGER}}),c=l.filter(h=>i>=h.effectiveThreshold);if(c.length>0){c.sort((C,P)=>{if(C.aceAlreadyPlayed&&!P.aceAlreadyPlayed)return-1;if(!C.aceAlreadyPlayed&&P.aceAlreadyPlayed)return 1;const O=C.card.suit===t,k=P.card.suit===t;return!O&&k?-1:O&&!k?1:C.card.rank==="2"&&P.card.rank==="3"?-1:C.card.rank==="3"&&P.card.rank==="2"?1:this.getCardStrengthScore(C.card)-this.getCardStrengthScore(P.card)});const h=c[0],f=h.aceAlreadyPlayed?`🎯 Asso di ${h.card.suit} già uscito! ${i.toFixed(1)} punti ≥ 0.6 - Uso ${h.card.rank}`:`🔥 ${i.toFixed(1)} punti con Asso sul tavolo - Uso ${h.card.rank}`;return{shouldUse:!0,recommendedCard:h.card,reason:f}}return{shouldUse:!1,recommendedCard:null,reason:l.some(h=>!h.aceAlreadyPlayed)?`💎 Conservo ${u[0].rank} per prendere l'Asso di ${u[0].suit} (${i.toFixed(1)} < soglia Asso)`:`💡 ${i.toFixed(1)} punti < 0.6 - Conservo carte strategiche anche se Assi usciti`}}shouldBeAggressiveForOpponentPrevention(e,n,o,t,i,a){const s=e.reduce((h,f)=>h+this.getCardValue(f),0);if(s<1)return{shouldBeAggressive:!1,trickValue:s,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Nessun punto sul tavolo - non serve aggressività"};const u=this.analyzeCurrentWinner(e,n,o,t,a);if(u.winnerIndex===-1)return{shouldBeAggressive:!1,trickValue:s,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Primo a giocare - nessun avversario da contrastare"};const g=a.players[i].team;if(!(u.winnerTeam!==g))return{shouldBeAggressive:!1,trickValue:s,opponentIsWinning:!1,aggressivenessLevel:"none",reason:"Il nostro team sta già vincendo - non serve aggressività"};let c,d;return e.some(h=>h.rank==="A")?(c="critical",d="CRITICO: Avversario sta prendendo un ASSO (1 punto) - IMPEDIRE ASSOLUTAMENTE!"):s>=3?(c="critical",d=`CRITICO: Avversario vince ${s.toFixed(1)} punti - IMPEDIRE A TUTTI I COSTI!`):s>=2?(c="high",d=`ALTO: Avversario vince ${s.toFixed(1)} punti - molto importante impedire`):s>=1.5?(c="medium",d=`MEDIO: Avversario vince ${s.toFixed(1)} punti - importante impedire`):s>=1?(c="medium",d=`MEDIO: Avversario vince ${s.toFixed(1)} punti - importante impedire`):(c="none",d="Troppo pochi punti per giustificare aggressività"),{shouldBeAggressive:c!=="none",trickValue:s,opponentIsWinning:!0,aggressivenessLevel:c,reason:d}}findBestCardToPreventOpponent(e,n,o,t,i){if(o){const s=e.filter(u=>u.suit===o);if(s.length>0){const u=s.filter(g=>this.canWinCurrentTrick(g,n,o,t));if(u.length>0){u.sort((l,c)=>this.getCardOrder(l)-this.getCardOrder(c));const g=u[0];return{recommendedCard:g,strategy:"same_suit",reason:`Uso ${g.rank} del seme per vincere con minimo spreco`}}return{recommendedCard:null,strategy:"cannot_win",reason:"Nessuna carta del seme può vincere"}}}const a=e.filter(s=>s.suit===t);if(a.length>0&&(i==="critical"||i==="high"&&a.some(u=>this.getCardValue(u)===0)||i==="medium"&&a.some(u=>this.getCardValue(u)===0&&this.getCardOrder(u)<=6))){const u=o===t,g=a.filter(l=>this.canWinCurrentTrick(l,n,o,t));if(!u&&g.length===0)return console.log("[AI] ⚠️ Evito briscole non vincenti quando non seguo briscola"),{reason:"Evito briscole non vincenti"};if(g.length>0){g.sort((d,m)=>{const h=this.getCardValue(d),f=this.getCardValue(m);return h!==f?h-f:this.getCardOrder(d)-this.getCardOrder(m)});const l=g[0],c=this.getCardValue(l);if((l.rank==="3"||l.rank==="2")&&i!=="critical"){const d=g.filter(m=>m.rank!=="3"&&m.rank!=="2");if(d.length>0){const m=d[0];return{recommendedCard:m,strategy:"trump_cut",reason:`Taglio con ${m.rank} di briscola (evito carte strategiche)`}}}return{recommendedCard:l,strategy:"trump_cut",reason:`Taglio con ${l.rank} di briscola (valore: ${c}, livello: ${i})`}}}return{recommendedCard:null,strategy:"cannot_win",reason:"Impossibile vincere la presa con le carte disponibili"}}getTeammateSupport(e,n,o,t,i,a){if(n.length===0)return{shouldSupport:!1,recommendedCard:null,reason:"Primo giocatore - nessun compagno da supportare",supportType:"NO_SUPPORT"};const s=this.analyzeCurrentWinner(n,o,t,0,i),u=i.players[a].team;if(!(s.winnerTeam===u&&s.winnerIndex!==a))return{shouldSupport:!1,recommendedCard:null,reason:"Compagno non sta vincendo",supportType:"NO_SUPPORT"};const l=n[s.winnerIndex];if(o?e.some(d=>d.suit===o):!1){const d=e.filter(k=>k.suit===o),h=["3","2","A","K","H"].includes(l.rank)||l.suit===t&&l.rank==="J",f=d.filter(k=>!this.canWinCurrentTrick(k,n,o,t)),C=f.filter(k=>this.getCardValue(k)>0);if(C.length>0){C.sort((w,N)=>{const L={A:1,K:2,H:3,J:4,2:5,3:6};return(L[w.rank]||999)-(L[N.rank]||999)});const k=this.getCardValue(C[0]),M=h?"imbattibile":"vincente";return{shouldSupport:!0,recommendedCard:C[0],reason:`Compagno ha ${l.rank} (${M}) - REGALO ${C[0].rank} (${k} punti) SENZA superarlo`,supportType:"GIVE_POINTS"}}const P=f.filter(k=>this.getCardValue(k)===0);if(P.length>0)return P.sort((k,M)=>this.getDiscardOrderScore(k)-this.getDiscardOrderScore(M)),{shouldSupport:!0,recommendedCard:P[0],reason:`Compagno sta vincendo - SCARTO carta senza punti: ${P[0].rank}`,supportType:"BE_STRATEGIC"};const O=d.filter(k=>this.getCardValue(k)>0);return O.sort((k,M)=>this.getDiscardOrderScore(k)-this.getDiscardOrderScore(M)),{shouldSupport:!0,recommendedCard:O[0],reason:`Compagno sta vincendo - SCARTO carta meno dolorosa: ${O[0].rank}`,supportType:"BE_STRATEGIC"}}else{if(!(l.suit===t)){console.log(`[TEAMMATE SUPPORT] 🚫 Compagno vince con ${l.rank} NON-briscola - NO taglio!`);const h=e.filter(f=>f.suit!==t);if(h.length>0){const f=h.filter(O=>this.getCardValue(O)>0);if(f.length>0)return f.sort((O,k)=>{const M={A:1,K:2,H:3,J:4,2:5,3:6};return(M[O.rank]||999)-(M[k.rank]||999)}),{shouldSupport:!0,recommendedCard:f[0],reason:`Compagno ha ${l.rank} NON-briscola - REGALO ${f[0].rank} (${this.getCardValue(f[0])} punti) SENZA tagliare`,supportType:"GIVE_POINTS"};const C=h.filter(O=>this.getCardValue(O)===0);if(C.length>0)return C.sort((O,k)=>this.getDiscardOrderScore(O)-this.getDiscardOrderScore(k)),{shouldSupport:!0,recommendedCard:C[0],reason:`Compagno vince con NON-briscola - NON taglio, uso carta senza punti: ${C[0].rank}`,supportType:"BE_STRATEGIC"};const P=h.filter(O=>this.getCardValue(O)>0);return P.sort((O,k)=>this.getDiscardOrderScore(O)-this.getDiscardOrderScore(k)),{shouldSupport:!0,recommendedCard:P[0],reason:`Compagno vince con NON-briscola - sacrifico ${P[0].rank} SENZA tagliare`,supportType:"BE_STRATEGIC"}}else{console.log("[TEAMMATE SUPPORT] ⚠️ Solo briscole disponibili ma compagno vince con NON-briscola!");const f=e.filter(C=>C.suit===t);return f.sort((C,P)=>{const O=this.getCardValue(C)>0,k=this.getCardValue(P)>0;return!O&&k?-1:O&&!k?1:this.getCardOrder(C)-this.getCardOrder(P)}),{shouldSupport:!0,recommendedCard:f[0],reason:`⚠️ SOLO briscole disponibili - uso la PIÙ DEBOLE: ${f[0].rank} (compagno ha ${l.rank} NON-briscola)`,supportType:"BE_STRATEGIC"}}}const m=e.filter(h=>h.suit!==t);if(m.length>0){const f=["3","2","A","K","H"].includes(l.rank)||l.suit===t&&l.rank==="J",C=m.filter(k=>this.getCardValue(k)>0);if(C.length>0){C.sort((M,w)=>{const N={A:1,K:2,H:3,J:4,2:5,3:6};return(N[M.rank]||999)-(N[w.rank]||999)});const k=f?"imbattibile":"vincente";return{shouldSupport:!0,recommendedCard:C[0],reason:`Compagno ha ${l.rank} (${k}) - REGALO ${C[0].rank} non-briscola (${this.getCardValue(C[0])} punti) SENZA tagliare`,supportType:"GIVE_POINTS"}}const P=m.filter(k=>this.getCardValue(k)===0);if(P.length>0)return P.sort((k,M)=>this.getDiscardOrderScore(k)-this.getDiscardOrderScore(M)),{shouldSupport:!0,recommendedCard:P[0],reason:`Compagno sta vincendo - NON taglio, uso carta senza punti: ${P[0].rank}`,supportType:"BE_STRATEGIC"};const O=m.filter(k=>this.getCardValue(k)>0);return O.sort((k,M)=>this.getDiscardOrderScore(k)-this.getDiscardOrderScore(M)),{shouldSupport:!0,recommendedCard:O[0],reason:`Compagno sta vincendo - non spreco briscole, sacrifico ${O[0].rank}`,supportType:"BE_STRATEGIC"}}else{const h=e.filter(C=>C.suit===t),f=h.filter(C=>!this.canWinCurrentTrick(C,n,o,t));return f.length>0?(f.sort((C,P)=>{const O=this.getCardValue(C)>0,k=this.getCardValue(P)>0;return!O&&k?-1:O&&!k?1:this.getDiscardOrderScore(C)-this.getDiscardOrderScore(P)}),{shouldSupport:!0,recommendedCard:f[0],reason:`Compagno già vincente - uso briscola DEBOLE SENZA superarlo: ${f[0].rank}`,supportType:"BE_STRATEGIC"}):(h.sort((C,P)=>{const O=this.getCardValue(C)>0,k=this.getCardValue(P)>0;return!O&&k?-1:O&&!k?1:this.getDiscardOrderScore(C)-this.getDiscardOrderScore(P)}),{shouldSupport:!0,recommendedCard:h[0],reason:`⚠️ SOLO briscole che superano compagno - uso la PIÙ DEBOLE: ${h[0].rank} (male minore)`,supportType:"BE_STRATEGIC"})}}}isHighUnsuperableCard(e,n){return!!(["3","2","A","K","H"].includes(e.rank)||e.suit===n&&e.rank==="J")}shouldNotWasteAce(e,n,o,t,i){if(e.rank!=="A")return{shouldAvoid:!1,reason:"Non è un asso"};if(this.canWinCurrentTrick(e,o,t.leadSuit,t.trumpSuit))return{shouldAvoid:!1,reason:"L'asso può prendere"};if(this.analyzeTeammatePosition(t,i).teammateIsWinning)return{shouldAvoid:!1,reason:"Il team sta prendendo"};const s=n.filter(u=>u!==e&&this.getCardValue(u)===0);return s.length>0?{shouldAvoid:!0,reason:"Asso sprecato: non prende e regala punti agli avversari",alternative:s[0]}:{shouldAvoid:!1,reason:"Nessuna alternativa disponibile"}}handleNoLeadSuit(e,n,o,t){if(e.some(u=>u.suit===o.leadSuit))return{reason:"Ha il seme di uscita"};const a=this.analyzeTeammatePosition(o,t),s=n.reduce((u,g)=>u+this.getCardValue(g),0);if(a.teammateIsWinning){const u=e.filter(g=>["A","K","Q","J"].includes(g.rank));if(u.length>0)return u.sort((g,l)=>{const c={A:4,K:3,Q:2,J:1};return(c[l.rank]||0)-(c[g.rank]||0)}),{recommendedCard:u[0],reason:`Compagno prende: do ${u[0].rank} di ${u[0].suit}`}}else{if(s>=1){const g=e.filter(c=>c.suit===o.trumpSuit);if(o.leadSuit===o.trumpSuit){const c=g.filter(d=>this.canWinCurrentTrick(d,n,o.leadSuit,o.trumpSuit));if(c.length>0)return c.sort((d,m)=>this.getCardOrder(d)-this.getCardOrder(m)),{recommendedCard:c[0],reason:`Seguo briscola vincente per ${s} punti con ${c[0].rank}`};console.log("[AI] ⚠️ Non posso vincere con briscole, evito di sprecarle")}else{const c=g.filter(d=>this.canWinCurrentTrick(d,n,o.leadSuit,o.trumpSuit));if(c.length>0)return c.sort((d,m)=>this.getCardOrder(d)-this.getCardOrder(m)),{recommendedCard:c[0],reason:`Taglio per ${s} punti con ${c[0].rank} di ${c[0].suit}`}}}const u=e.filter(g=>["4","5","6","7"].includes(g.rank));if(u.length>0)return{recommendedCard:u[0],reason:`Scarto ${u[0].rank} (non spreco 2 e 3)`}}return{reason:"Nessuna strategia applicabile"}}handleLastPlayer(e,n,o,t){if(!n||n.length!==3)return{reason:"Non è l'ultimo giocatore"};const i=e.filter(a=>this.canWinCurrentTrick(a,n,o.leadSuit,o.trumpSuit));if(i.length>0){const a=i.filter(s=>["A","K","Q","J"].includes(s.rank));if(a.length>0)return a.sort((s,u)=>{const g={A:4,K:3,Q:2,J:1};return(g[u.rank]||0)-(g[s.rank]||0)}),{recommendedCard:a[0],reason:`Ultimo giocatore: prendo con ${a[0].rank} di ${a[0].suit}`}}return{reason:"Non posso prendere con carte di valore"}}handleAceByTeamStatus(e,n,o,t){if(!n||n.length===0)return{reason:"Nessuna presa in corso"};const a=e.filter(g=>["A","K","Q","J"].includes(g.rank)&&g.suit!==o.trumpSuit).filter(g=>this.canWinCurrentTrick(g,n,o.leadSuit,o.trumpSuit));if(a.length>0)return a.sort((g,l)=>{const c={A:4,K:3,Q:2,J:1};return(c[l.rank]||0)-(c[g.rank]||0)}),{recommendedCard:a[0],reason:`Prendo sempre con carte di valore non-briscola: ${a[0].rank} di ${a[0].suit}`};const s=this.analyzeTeammatePosition(o,t),u=e.filter(g=>g.rank==="A");if(s.teammateIsWinning){if(u.length>0)return{recommendedCard:u[0],reason:`Team prende: butto asso ${u[0].rank} di ${u[0].suit}`}}else{const g=e.some(l=>l.rank!=="A");if(u.length>0&&g)return{filteredCards:e.filter(c=>c.rank!=="A"),reason:"Team NON prende: conservo asso, uso alternative"}}return{reason:"Gestione asso non applicabile"}}analyzeTeammatePosition(e,n){if(!e.currentTrick||e.currentTrick.length===0)return{teammateIsWinning:!1};let o=0,t=-1,i=-1,a=!1;for(let l=0;l<e.currentTrick.length;l++){const c=e.currentTrick[l];if(c.suit===e.trumpSuit){a=!0;const d=this.getCardOrder(c);d>t&&(t=d,o=l)}else if(c.suit===e.leadSuit&&!a){const d=this.getCardOrder(c);d>i&&(i=d,o=l)}}const s=((e.leadPlayer??0)+o)%4,u=e.players[s].team,g=e.players[n].team;return{teammateIsWinning:u===g&&s!==n}}applyGameRules(e,n,o){const t=n.currentTrick||[],i=this.handleLastPlayer(e,t,n,o);if(i.recommendedCard)return i;const a=this.handleAceByTeamStatus(e,t,n,o);if(a.recommendedCard)return a;a.filteredCards&&(e=a.filteredCards);const s=this.handleNoLeadSuit(e,t,n,o);if(s.recommendedCard)return s;const u=e.filter(g=>g.rank==="A");for(const g of u){const l=this.shouldNotWasteAce(g,e,t,n,o);if(l.shouldAvoid&&l.alternative)return{recommendedCard:l.alternative,reason:l.reason}}return{reason:"Nessuna regola applicabile, usa logica normale"}}};const fr=(r,e,n,o,t,i=null)=>{const a=B(r),s=n.reduce((u,g)=>u+B(g),0);if(s>=1){if(j(r,n,o.leadSuit,o.trumpSuit))return{isWaste:!1,reason:`REGOLA ASSOLUTA: ${s.toFixed(1)} punti sul tavolo e posso vincere - MAI SPRECO!`,severity:"low",alternativesExist:!1};if(n.length>0&&o.leadPlayer!==void 0){const g=fe(n,o.leadSuit,o.trumpSuit,o.leadPlayer),l=(o.leadPlayer+g)%4,c=o.players[t].team;if(o.players[l].team===c&&l!==t)return{isWaste:!1,reason:`REGOLA ASSOLUTA BIS: ${s.toFixed(1)} punti e compagno vince - aiutare MAI è spreco!`,severity:"low",alternativesExist:!1}}}if(a===0)return{isWaste:!1,reason:"Carta senza valore - sempre sicura da giocare",severity:"low",alternativesExist:!1};if(r.rank==="3"||r.rank==="2"||r.rank==="A"){const u=j(r,n,o.leadSuit,o.trumpSuit);if(u){if(new x().isObviousWinSituation(r,n,o.leadSuit,o.trumpSuit,s))return{isWaste:!1,reason:`Situazione di vincita ovvia con ${r.rank} - sempre giustificato`,severity:"low",alternativesExist:!1};const d=r.suit===o.trumpSuit,m=r.rank==="2",h=r.rank==="3"&&!d;if(m){if(d){if(s>=2)return{isWaste:!1,reason:`Briscola strategica (${r.rank}) giustificata con ${s} punti sul tavolo`,severity:"low",alternativesExist:!1}}else if(s>=2)return{isWaste:!1,reason:`2 strategico giustificato con ${s} punti sul tavolo`,severity:"low",alternativesExist:!1}}else if(h){if(s>=1)return{isWaste:!1,reason:`3 non di briscola OTTIMO per prendere ${s} punti`,severity:"low",alternativesExist:!1};if(n.length<=1)return{isWaste:!1,reason:`3 non di briscola per controllo e apertura (${n.length+1}° a giocare)`,severity:"low",alternativesExist:!1}}if(n.length>=2&&!n.some(C=>C.rank==="A"||C.rank==="3"||C.rank==="2"))return{isWaste:!1,reason:`Carta strategica (${r.rank}) per anticipare possibili assi avversari`,severity:"low",alternativesExist:!1}}let g=!1;if(n.length>0&&o.leadPlayer!==void 0){const l=fe(n,o.leadSuit,o.trumpSuit,o.leadPlayer),c=(o.leadPlayer+l)%4,d=o.players[t].team;g=o.players[c].team===d&&c!==t}if(!u&&!g)return{isWaste:!0,reason:`CRITICO: ${r.rank} andrebbe agli avversari!`,severity:"critical",alternativesExist:e.length>1}}if(i&&cr(r,i)&&!j(r,n,o.leadSuit,o.trumpSuit)){let g=!1;if(o.leadSuit&&r.suit===o.leadSuit?g=e.filter(c=>c.suit===o.leadSuit).filter(c=>c.id!==r.id).length>0:g=e.filter(l=>l.id!==r.id).length>0,g)return{isWaste:!0,reason:"DOMINANZA: Carta dominante sprecata, ho alternative",severity:"high",alternativesExist:!0}}return s===0?{isWaste:!0,reason:`PRESA SENZA VALORE: Non do ${a} punti per 0 punti`,severity:"medium",alternativesExist:e.some(u=>B(u)===0)}:r.rank==="A"&&s<3?{isWaste:!0,reason:`ASSO: Richiede almeno 3 punti, presa vale ${s}`,severity:"high",alternativesExist:e.some(u=>u.rank!=="A")}:r.rank==="K"&&s<2?{isWaste:!0,reason:`RE: Richiede almeno 2 punti, presa vale ${s}`,severity:"medium",alternativesExist:e.some(u=>u.rank!=="K")}:a>0&&s<a*.8?{isWaste:!0,reason:`VALORE: Carta vale ${a}, presa solo ${s}`,severity:"low",alternativesExist:e.some(u=>B(u)<a)}:{isWaste:!1,reason:"Carta appropriata per questa situazione",severity:"low",alternativesExist:!1}},pr=(r,e,n,o)=>{let t=null;try{t=Re(n)}catch(c){console.warn("Errore nell'analisi memoria:",c)}const i=r.map(c=>fr(c,r,e,n,o,t)),a=r.filter((c,d)=>!i[d].isWaste);let s=a;if(a.length===0){console.log("[SISTEMA ANTI-SPRECO] ⚠️ Tutte le carte sono spreco, scelgo male minore");const c=Math.min(...i.map(d=>({low:1,medium:2,high:3,critical:4})[d.severity]));s=r.filter((d,m)=>({low:1,medium:2,high:3,critical:4})[i[m].severity]===c)}let u=null,g="",l=0;if(s.length>0)if(e.length===0)u=s[0],g="Carta strategica per aprire",l=.7;else{const c=s.filter(d=>j(d,e,n.leadSuit,n.trumpSuit));if(c.length>0)c.sort((d,m)=>B(d)-B(m)),u=c[0],g="Carta più debole che può vincere",l=.85;else{const d=n.leadSuit===n.trumpSuit;if(s.filter(h=>h.suit===n.trumpSuit&&!d).length>0){const h=s.filter(f=>f.suit!==n.trumpSuit);h.length>0?(h.sort((f,C)=>B(f)-B(C)),u=h[0],g="Evito briscole non vincenti, scarto carta normale",l=.8):(s.sort((f,C)=>B(f)-B(C)),u=s[0],g="Solo briscole disponibili, uso quella di minor valore",l=.5)}else s.sort((h,f)=>B(h)-B(f)),u=s[0],g="Carta di minor valore (non posso vincere)",l=.6}}return{optimalCards:s,wasteAnalysis:i,recommendation:{bestCard:u,reason:g,confidence:l}}},Z=(r,e,n,o)=>{var s,u,g;let t=B(r)*10;r.suit===n.trumpSuit&&(t+=15,[R.Ace,R.Three,R.Two].includes(r.rank)&&(t+=20));const i=e.highCardsRemaining[((s=r.suit)==null?void 0:s.toString())||""]||[];return i.length===1&&i[0].rank===r.rank&&(t+=25),(((g=e.playedBySuit[((u=r.suit)==null?void 0:u.toString())||""])==null?void 0:g.length)||0)<=2&&(t+=10),t},Ar=(r,e)=>{if(!r.currentTrick||r.currentTrick.length===0)return{teammateIsWinning:!1,teammatePosition:-1,winningCard:null,shouldSupport:!1};const n=r.players[e].team,o=r.leadPlayer??0;let t=-1;for(let c=0;c<r.currentTrick.length;c++){const d=(o+c)%4;if(r.players[d].team===n&&d!==e){t=c;break}}let i=0,a=r.currentTrick[0],s=0;a.suit===r.trumpSuit?s=1e3+J(a):a.suit===r.leadSuit&&(s=J(a));for(let c=1;c<r.currentTrick.length;c++){const d=r.currentTrick[c];let m=0;d.suit===r.trumpSuit?m=1e3+J(d):d.suit===r.leadSuit&&(m=J(d)),m>s&&(s=m,i=c,a=d)}const u=(o+i)%4,l=(u>=0?r.players[u].team:-1)===n&&u!==e;return{teammateIsWinning:l,teammatePosition:t,winningCard:a,shouldSupport:l&&t>=0}},hr=(r,e,n)=>{const o=r.suit;if(r.rank==="A"){const t=e.some(a=>a.suit===o&&a.rank==="2"),i=e.some(a=>a.suit===o&&a.rank==="3");if(t&&i)return console.log(`[ASSO SICURO] 🎯 Ho la maraffa in ${o} - Asso sicuro!`),!0}else if(r.rank==="K"){const t=e.some(s=>s.suit===o&&s.rank==="A"),i=e.some(s=>s.suit===o&&s.rank==="2"),a=e.some(s=>s.suit===o&&s.rank==="3");if(t&&i&&a)return console.log(`[RE SICURO] 🎯 Ho tutte le carte superiori in ${o} - Re sicuro!`),!0}if(r.rank==="A"){const t=n.some(a=>a.suit===o&&a.rank==="2"),i=n.some(a=>a.suit===o&&a.rank==="3");return t&&i?(console.log(`[ASSO SICURO] 🎯 2 e 3 di ${o} già usciti - Asso sicuro!`),!0):(console.log(`[ASSO NON SICURO] ⚠️ Asso di ${o} non sicuro - 2 e 3 ancora in gioco`),!1)}else if(r.rank==="K"){const t=n.some(s=>s.suit===o&&s.rank==="A"),i=n.some(s=>s.suit===o&&s.rank==="2"),a=n.some(s=>s.suit===o&&s.rank==="3");return t&&i&&a?(console.log(`[RE SICURO] 🎯 A, 2 e 3 di ${o} già usciti - Re sicuro!`),!0):(console.log(`[RE NON SICURO] ⚠️ Re di ${o} non sicuro - A, 2 o 3 ancora in gioco`),!1)}return!1},Cr=(r,e,n,o=[])=>{var u;const t=((u=e.currentTrick)==null?void 0:u.reduce((g,l)=>g+n.getCardValue(l),0))||0;if(t>=1){const g=r.filter(l=>{var c,d;return n.canWinCurrentTrick(l,e.currentTrick||[],((d=(c=e.currentTrick)==null?void 0:c[0])==null?void 0:d.suit)||null,e.trumpSuit)});if(g.length>0){g.sort((d,m)=>{const h=n.getCardValue(d),f=n.getCardValue(m);return h===0&&f>0?-1:f===0&&h>0?1:n.getCardOrder(d)-n.getCardOrder(m)});const l=g[0],c=t+n.getCardValue(l);return console.log(`[COOPERATIVA] 🚨 REGOLA ASSOLUTA: ${t.toFixed(1)} punti sul tavolo - PRENDO con ${l.rank} di ${l.suit}!`),{canWin:!0,bestCard:l,pointsSecured:c}}}const i=r.filter(g=>{var l,c;return n.canWinCurrentTrick(g,e.currentTrick||[],((c=(l=e.currentTrick)==null?void 0:l[0])==null?void 0:c.suit)||null,e.trumpSuit)});if(i.length===0)return{canWin:!1,bestCard:null,pointsSecured:0};const a=i.filter(g=>g.suit!==e.trumpSuit&&n.getCardValue(g)>0);if(a.length>0)if(!e.currentTrick||e.currentTrick.length===0){const l=a.filter(c=>c.rank==="A"||c.rank==="K"?hr(c,r,o):!0);if(l.length>0){l.sort((m,h)=>n.getCardValue(h)-n.getCardValue(m));const c=l[0],d=t+n.getCardValue(c);return console.log(`[CARTA VERIFICATA] ✅ Gioco ${c.rank} di ${c.suit} come prima carta - SICURO!`),{canWin:!0,bestCard:c,pointsSecured:d}}else return console.log("[CARTE BLOCCATE] ❌ Nessuna carta di punti sicura come prima carta - salto strategia punti"),{canWin:!1,bestCard:null,pointsSecured:0}}else{a.sort((d,m)=>n.getCardValue(m)-n.getCardValue(d));const l=a[0],c=t+n.getCardValue(l);return{canWin:!0,bestCard:l,pointsSecured:c}}const s=i.filter(g=>g.suit===e.trumpSuit);if(s.length>0){s.sort((c,d)=>n.getCardValue(c)-n.getCardValue(d));const g=s[0],l=t+n.getCardValue(g);if(t>=2||e.trickNumber===10)return{canWin:!0,bestCard:g,pointsSecured:l}}return{canWin:!1,bestCard:null,pointsSecured:0}},Tr=(r,e,n,o)=>{var m,h,f,C,P,O,k,M,w,N,L,V,v;console.log(`[COLLABORATIVA AVANZATA] 🤝 Avvio strategia TEAM-FIRST per giocatore ${e}`);const t=new x,i=Ar(r,e),a=((m=r.currentTrick)==null?void 0:m.length)===3,s=((h=r.currentTrick)==null?void 0:h.reduce((T,E)=>T+t.getCardValue(E),0))||0,g=(r.trickNumber??1)>=8;if(console.log(`[COLLABORATIVA AVANZATA] 📊 Posizione: ${a?"ULTIMO":"NON ULTIMO"}, Presa: ${s.toFixed(1)} punti`),i.teammateIsWinning&&r.currentTrick&&r.currentTrick.length>0){const T=(f=r.currentTrick[0])==null?void 0:f.suit,E=T?n.some(p=>p.suit===T):!1;console.log(`[COLLABORATIVA AVANZATA] 🚨 RILEVATO COMPAGNO VINCENTE: ${(C=i.winningCard)==null?void 0:C.rank} di ${(P=i.winningCard)==null?void 0:P.suit}`),console.log(`[COLLABORATIVA AVANZATA] 📊 Presa attuale: ${s.toFixed(1)} punti, Posso seguire seme: ${E?"SÌ":"NO"}`);const y=n.filter(p=>p.suit===r.trumpSuit),$=n.filter(p=>p.suit!==r.trumpSuit);if(E){const p=n.filter(A=>A.suit===T);if(p.length>0){console.log(`[COLLABORATIVA AVANZATA] ✅ SEGUO SEME: ${p.length} carte di ${T} disponibili`);const A=p.filter(S=>_(S));if(A.length>0){A.sort((I,b)=>{const F={A:1,K:2,H:3,J:4};return(F[I.rank]||999)-(F[b.rank]||999)});const S=A[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 REGALO PUNTI SEGUENDO SEME: ${S.rank} di ${S.suit} (${t.getCardValue(S)} punti)`),{strategy:"support",recommendedCard:S,reason:`🎁 SUPPORTO OTTIMALE: Compagno vincente → regalo ${S.rank} del seme richiesto (${t.getCardValue(S)} punti)`}}return p.sort((S,I)=>t.getDiscardOrderScore(S)-t.getDiscardOrderScore(I)),console.log(`[COLLABORATIVA AVANZATA] 🎯 SCARTO SEGUENDO SEME: ${p[0].rank} di ${p[0].suit}`),{strategy:"support",recommendedCard:p[0],reason:`🎯 SUPPORTO PULITO: Compagno vincente → scarto ${p[0].rank} seguendo il seme`}}}if(!E&&$.length>0){console.log(`[COLLABORATIVA AVANZATA] 🛡️ DIVIETO BRISCOLE: ${$.length} carte non-briscola disponibili, ${y.length} briscole VIETATE`);const p=$.filter(S=>_(S));if(p.length>0){p.sort((I,b)=>{const F={A:1,K:2,H:3,J:4};return(F[I.rank]||999)-(F[b.rank]||999)});const S=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO PUNTI: ${S.rank} di ${S.suit} (${t.getCardValue(S)} punti) invece di sprecare briscole`),{strategy:"support",recommendedCard:S,reason:`🛡️ ANTI-SPRECO CRITICO: Compagno vincente → regalo ${S.rank} non-briscola (${t.getCardValue(S)} punti) invece di sprecare briscole`}}const A=$.filter(S=>t.getCardValue(S)===0);if(A.length>0)return A.sort((S,I)=>t.getDiscardOrderScore(S)-t.getDiscardOrderScore(I)),console.log(`[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO SCARTO: ${A[0].rank} di ${A[0].suit} invece di sprecare briscole`),{strategy:"support",recommendedCard:A[0],reason:`🛡️ ANTI-SPRECO TOTALE: Compagno vincente → scarto ${A[0].rank} non-briscola invece di sprecare briscole`};if($.length>0)return $.sort((S,I)=>t.getDiscardOrderScore(S)-t.getDiscardOrderScore(I)),console.log(`[COLLABORATIVA AVANZATA] ⚠️ SACRIFICIO MINIMO: ${$[0].rank} di ${$[0].suit} per evitare briscole`),{strategy:"support",recommendedCard:$[0],reason:`⚠️ SACRIFICIO MINIMO: Compagno vincente → sacrifico ${$[0].rank} non-briscola per conservare le briscole`}}if(y.length>0&&$.length===0){if(console.log("[COLLABORATIVA AVANZATA] ⚠️ CASO ESTREMO: Solo briscole disponibili, compagno sta vincendo"),s>=1){const p=y.filter(A=>t.getCardValue(A)===0);if(p.length>0)return p.sort((A,S)=>t.getCardStrengthScore(A)-t.getCardStrengthScore(S)),console.log(`[COLLABORATIVA AVANZATA] 🎯 BRISCOLA DEBOLE GIUSTIFICATA: ${p[0].rank} di ${p[0].suit} per ${s.toFixed(1)} punti`),{strategy:"support",recommendedCard:p[0],reason:`🎯 BRISCOLA GIUSTIFICATA: Solo briscole disponibili, uso la più debole ${p[0].rank} per ${s.toFixed(1)} punti in presa`}}return y.sort((p,A)=>t.getDiscardOrderScore(p)-t.getDiscardOrderScore(A)),console.log(`[COLLABORATIVA AVANZATA] 😞 BRISCOLA FORZATA: ${y[0].rank} di ${y[0].suit} (nessuna alternativa)`),{strategy:"support",recommendedCard:y[0],reason:`😞 BRISCOLA FORZATA: Compagno vincente ma nessuna alternativa disponibile, uso la briscola più debole ${y[0].rank}`}}}const l=Cr(n,r,t,o.playedCards);if(l.canWin&&l.bestCard)return console.log("[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 0: POSSO VINCERE CON CARTE DI PUNTI!"),console.log(`[COLLABORATIVA AVANZATA] 💰 PUNTI ASSICURATI AL TEAM: ${l.pointsSecured.toFixed(1)} punti con ${l.bestCard.rank} di ${l.bestCard.suit}`),{strategy:"compete",recommendedCard:l.bestCard,reason:`🎯 PUNTI ASSICURATI: ${l.pointsSecured.toFixed(1)} punti per il team con ${l.bestCard.rank} di ${l.bestCard.suit}! ${l.bestCard.suit===r.trumpSuit?"Briscola strategica":"Carta di punti perfetta"}`};if(i.teammateIsWinning&&i.winningCard){const T=i.winningCard,E=(k=(O=r.currentTrick)==null?void 0:O[0])==null?void 0:k.suit,y=T.rank==="3"||T.rank==="2"||T.rank==="A",$=E?n.some(p=>p.suit===E):!0;if(y&&!$){console.log(`[COLLABORATIVA AVANZATA] 🚨 REGOLA SUPREMA: Compagno vince con ${T.rank} forte e non posso seguire ${E} - DEVO DARE PUNTI!`);const p=n.filter(A=>A.rank==="A"||A.rank==="K"||A.rank==="H"||A.rank==="J");if(p.length>0){p.sort((S,I)=>{const b={A:4,K:3,H:2,J:1};return(b[I.rank]||0)-(b[S.rank]||0)});const A=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 PUNTI AL COMPAGNO: ${A.rank} di ${A.suit} (valore: ${t.getCardValue(A)}) per carta forte ${T.rank}`),{strategy:"support",recommendedCard:A,reason:`🚨 REGOLA SUPREMA: Compagno vince con ${T.rank} forte, non posso seguire ${E} → SEMPRE dare punti! (${A.rank})`}}}}if(i.teammateIsWinning&&s>=1){console.log(`[COLLABORATIVA AVANZATA] 🚨 REGOLA ASSOLUTA BIS: Compagno vince ${s.toFixed(1)} punti - AIUTO SEMPRE!`);const T=n.filter(E=>_(E));if(T.length>0){T.sort((y,$)=>y.rank==="A"&&$.rank!=="A"?-1:$.rank==="A"&&y.rank!=="A"?1:t.getCardValue($)-t.getCardValue(y));const E=T[0];return console.log(`[COLLABORATIVA AVANZATA] 🤝 AIUTO COMPAGNO: ${E.rank} di ${E.suit} (valore: ${t.getCardValue(E)})`),{strategy:"support",recommendedCard:E,reason:`REGOLA ASSOLUTA: Aiuto compagno che vince ${s.toFixed(1)} punti con ${E.rank}`}}}if(a&&i.teammateIsWinning){const T=r.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 1 ATTIVATA: Ultimo giocatore + Team vincente! ${T?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),i.winningCard&&i.winningCard.rank==="3"&&i.winningCard.suit!==r.trumpSuit){const A=n.find(S=>S.rank==="A"&&S.suit===i.winningCard.suit);if(A)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO ULTIMO: Compagno ha giocato 3 di ${i.winningCard.suit}, gioco Asso di ${A.suit}! ${T?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:A,reason:`🏆 RICONOSCIMENTO 3 (ULTIMO): Compagno ha giocato 3 di ${i.winningCard.suit} per prendere - gioco Asso per massimizzare punti! ${T?"(STRATEGIA PRIME MANI!)":""}`}}if(i.winningCard&&t.getCardValue(i.winningCard)>0){console.log(`[COLLABORATIVA AVANZATA] 🔥 COMPAGNO PRENDE CON PUNTI: ${i.winningCard.rank} (${t.getCardValue(i.winningCard)} punti)`);const A=n.filter(I=>I.rank==="A");if(A.length>0){const I=A[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno prende con punti, gioco Asso di ${I.suit}!`),{strategy:"support",recommendedCard:I,reason:`🏆 ASSO PRIORITARIO: Compagno prende con carta di punti (${i.winningCard.rank}) = SEMPRE giocare l'Asso! (${t.getCardValue(I)} punto)`}}const S=["J","H","K"];for(const I of S){const b=n.find(F=>F.rank===I);if(b)return console.log(`[COLLABORATIVA AVANZATA] 💎 CARTA ALTERNATIVA: Nessun asso, gioco ${b.rank} di ${b.suit}`),{strategy:"support",recommendedCard:b,reason:`💎 SUPPORTO PRIORITARIO: Compagno prende con punti, gioco ${b.rank} (${t.getCardValue(b)} punti)`}}}const y=K(n).sort((A,S)=>t.getCardValue(S)-t.getCardValue(A));if(y.length>0){const A=y[0];return console.log(`[COLLABORATIVA AVANZATA] 🔥 VALORIZZAZIONE MASSIMA: ${A.rank} di ${A.suit} (${t.getCardValue(A)} punti)`),{strategy:"support",recommendedCard:A,reason:`🎯 TEAM-FIRST P1: Ultimo giocatore + team vincente = SEMPRE valorizzare! Gioco ${A.rank} (${t.getCardValue(A)} punti)`}}const $=n.filter(A=>A.rank==="A");if($.length>0){const S=$.find(I=>{var b,F;return I.suit===((F=(b=r.currentTrick)==null?void 0:b[0])==null?void 0:F.suit)})||$.sort((I,b)=>t.getCardValue(b)-t.getCardValue(I))[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO MASSIMIZZAZIONE: Team vincente + ultimo giocatore = gioco Asso di ${S.suit} per massimizzare punti!`),{strategy:"support",recommendedCard:S,reason:`🏆 MASSIMIZZAZIONE TEAM: Team vincente + ultimo giocatore = SEMPRE giocare l'Asso! (${t.getCardValue(S)} punti)`}}const p=n.sort((A,S)=>t.getCardValue(S)-t.getCardValue(A))[0];return{strategy:"support",recommendedCard:p,reason:`🎯 MASSIMIZZAZIONE PUNTI: Ultimo giocatore + team vincente = gioco carta di maggior valore (${t.getCardValue(p)} punti)`}}if(((M=r.currentTrick)==null?void 0:M.length)===2&&i.teammateIsWinning&&i.winningCard){const T=i.winningCard;if(me(T,o,r)||t.isObviousWinSituation(T,r.currentTrick||[],((N=(w=r.currentTrick)==null?void 0:w[0])==null?void 0:N.suit)||null,r.trumpSuit,s)){const y=r.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] 🚀 PRIORITÀ ASSOLUTA 2 ATTIVATA: Terzo giocatore + compagno imbattibile! ${y?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),T.rank==="3"&&T.suit!==r.trumpSuit){const p=n.find(A=>A.rank==="A"&&A.suit===T.suit);if(p)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO TERZO: Compagno ha giocato 3 di ${T.suit}, gioco Asso di ${p.suit}! ${y?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:p,reason:`🏆 RICONOSCIMENTO 3 (TERZO): Compagno ha giocato 3 di ${T.suit} per prendere - gioco Asso per massimizzare punti! ${y?"(STRATEGIA PRIME MANI!)":""}`}}const $=K(n).sort((p,A)=>t.getCardValue(A)-t.getCardValue(p));if($.length>0){const p=$[0];return console.log(`[COLLABORATIVA AVANZATA] 🚀 VALORIZZAZIONE TERZO GIOCATORE: ${p.rank} di ${p.suit} (${t.getCardValue(p)} punti) - Compagno ha ${T.rank} imbattibile!`),{strategy:"support",recommendedCard:p,reason:`🚀 TEAM-FIRST P2: Terzo giocatore + compagno imbattibile (${T.rank}) = SEMPRE valorizzare! Gioco ${p.rank} (${t.getCardValue(p)} punti)`}}}}if(((L=r.currentTrick)==null?void 0:L.length)===1&&i.teammateIsWinning&&i.winningCard){const T=i.winningCard;if(T.rank==="3"&&T.suit!==r.trumpSuit||T.rank==="2"&&T.suit!==r.trumpSuit||T.suit===r.trumpSuit&&(T.rank==="3"||T.rank==="2")){const y=r.trickNumber<=3;if(console.log(`[COLLABORATIVA AVANZATA] ⚡ PRIORITÀ ASSOLUTA 3 ATTIVATA: Secondo giocatore + compagno primo con carta super forte! ${y?"(PRIME MANI - COOPERAZIONE CRITICA!)":""}`),T.rank==="3"&&T.suit!==r.trumpSuit){const p=n.find(A=>A.rank==="A"&&A.suit===T.suit);if(p)return console.log(`[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno ha giocato 3 di ${T.suit}, gioco Asso di ${p.suit}! ${y?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"support",recommendedCard:p,reason:`🏆 RICONOSCIMENTO 3: Compagno ha giocato 3 di ${T.suit} per prendere - gioco Asso per massimizzare punti! ${y?"(STRATEGIA PRIME MANI!)":""}`}}const $=K(n).filter(p=>{var A,S;return!t.canWinCurrentTrick(p,r.currentTrick||[],((S=(A=r.currentTrick)==null?void 0:A[0])==null?void 0:S.suit)||null,r.trumpSuit)}).sort((p,A)=>t.getCardValue(A)-t.getCardValue(p));if($.length>0){const p=$[0];return console.log(`[COLLABORATIVA AVANZATA] ⚡ VALORIZZAZIONE SECONDO GIOCATORE: ${p.rank} di ${p.suit} (${t.getCardValue(p)} punti) - Compagno primo con ${T.rank} super forte!`),{strategy:"support",recommendedCard:p,reason:`⚡ TEAM-FIRST P3: Secondo giocatore + compagno primo con carta super forte (${T.rank}) = valorizzare senza competere! Gioco ${p.rank} (${t.getCardValue(p)} punti)`}}}}if(!i.teammateIsWinning&&s>0){if(console.log(`[COLLABORATIVA AVANZATA] 🛡️ AVVERSARI VINCENTI: ${s.toFixed(1)} punti in gioco!`),a){const E=r.trickNumber<=3;console.log(`[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + AVVERSARI VINCENTI! ${E?"(PRIME MANI - RIBALTAMENTO CRITICO!)":""}`);const y=n.filter($=>$.rank==="A");if(y.length>0){const $=y.filter(p=>{var A,S;return t.canWinCurrentTrick(p,r.currentTrick||[],((S=(A=r.currentTrick)==null?void 0:A[0])==null?void 0:S.suit)||null,r.trumpSuit)});if($.length>0){const A=$.find(S=>{var I,b;return S.suit===((b=(I=r.currentTrick)==null?void 0:I[0])==null?void 0:b.suit)})||$.sort((S,I)=>t.getCardValue(I)-t.getCardValue(S))[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 RIBALTAMENTO ASSO: ${A.rank} di ${A.suit} ribalta ${s.toFixed(1)} punti! ${E?"(STRATEGIA PRIME MANI PERFETTA!)":""}`),{strategy:"compete",recommendedCard:A,reason:`🏆 RIBALTAMENTO CRITICO: Ultimo giocatore con Asso di ${A.suit} ribalta ${s.toFixed(1)} punti agli avversari! ${E?"(PRIME MANI!)":""}`}}}}if(s>=1){console.log(`[COLLABORATIVA AVANZATA] 🚨 RECUPERO AGGRESSIVO: ${s.toFixed(1)} punti da recuperare!`);const y=(r.currentTrick||[]).filter(p=>p.suit===r.trumpSuit);if(y.length>0){const p=y.reduce((S,I)=>t.getCardOrder(I)>t.getCardOrder(S)?I:S);if(["4","5","6","7"].includes(p.rank)&&s<2){console.log(`[COLLABORATIVA AVANZATA] 🛡️ ANTI-SPRECO: Avversario ha tagliato con ${p.rank} per solo ${s.toFixed(1)} punti - non ritaglio con briscole strategiche`);const S=n.filter(I=>I.suit!==r.trumpSuit);if(S.length>0){S.sort((b,F)=>t.getDiscardOrderScore(b)-t.getDiscardOrderScore(F));const I=S[0];return console.log(`[COLLABORATIVA AVANZATA] 🗑️ SCARTO INTELLIGENTE: ${I.rank} di ${I.suit} invece di sprecare briscole`),{strategy:"support",recommendedCard:I,reason:`🛡️ ANTI-SPRECO: Avversario ha tagliato con briscola debole per pochi punti - scarto ${I.rank} invece di sprecare briscole strategiche`}}}}const $=n.filter(p=>{var A,S;return t.canWinCurrentTrick(p,r.currentTrick||[],((S=(A=r.currentTrick)==null?void 0:A[0])==null?void 0:S.suit)||null,r.trumpSuit)});if($.length>0){const p=$.filter(I=>I.suit!==r.trumpSuit);if(p.length>0){p.sort((b,F)=>{const D=Z(b,o,r),$e=Z(F,o,r);return D-$e});const I=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 RECUPERO NON-BRISCOLA: ${I.rank} di ${I.suit} per ${s.toFixed(1)} punti`),{strategy:"compete",recommendedCard:I,reason:`🎯 RECUPERO OTTIMALE: Non-briscola ${I.rank} recupera ${s.toFixed(1)} punti senza sprecare briscole!`}}if(s>=1){const I=$.filter(b=>b.suit===r.trumpSuit&&(b.rank==="7"||b.rank==="6"||b.rank==="5"||b.rank==="4"));if(I.length>0){I.sort((F,D)=>t.getCardStrengthScore(F)-t.getCardStrengthScore(D));const b=I[0];return console.log(`[COLLABORATIVA AVANZATA] 🎯 RECUPERO OTTIMALE: Briscola bassa ${b.rank} di ${b.suit} per ${s.toFixed(1)} punti!`),{strategy:"compete",recommendedCard:b,reason:`🎯 RECUPERO OTTIMALE: Briscola bassa ${b.rank} recupera ${s.toFixed(1)} punti dagli avversari!`}}}const S=$.sort((I,b)=>{const F=Z(I,o,r),D=Z(b,o,r);return F-D})[0];return console.log(`[COLLABORATIVA AVANZATA] 🚨 RECUPERO CON: ${S.rank} di ${S.suit} - Salvo ${s.toFixed(1)} punti!`),{strategy:"compete",recommendedCard:S,reason:`🚨 RECUPERO AGGRESSIVO: Avversari vincenti con ${s.toFixed(1)} punti, recupero con ${S.rank}!`}}}if(s===0){console.log("[COLLABORATIVA AVANZATA] 🛡️ PRESA SENZA VALORE: 0 punti - evito briscole a tutti i costi");const E=n.filter(y=>y.suit!==r.trumpSuit);if(E.length>0){const y=(v=(V=r.currentTrick)==null?void 0:V[0])==null?void 0:v.suit;if(y?E.some(p=>p.suit===y):!1){const p=E.filter(A=>A.suit===y);return p.sort((A,S)=>t.getDiscardOrderScore(A)-t.getDiscardOrderScore(S)),console.log(`[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SEGUO SEME: ${p[0].rank} di ${p[0].suit}`),{strategy:"support",recommendedCard:p[0],reason:`🛡️ CONSERVAZIONE: Presa da 0 punti → seguo seme con ${p[0].rank} per non sprecare briscole`}}else{const p=E.filter(A=>t.getCardValue(A)===0);return p.length>0?(p.sort((A,S)=>t.getDiscardOrderScore(A)-t.getDiscardOrderScore(S)),console.log(`[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SCARTO PULITO: ${p[0].rank} di ${p[0].suit}`),{strategy:"support",recommendedCard:p[0],reason:`🛡️ SCARTO OTTIMALE: Presa da 0 punti → scarto ${p[0].rank} non-briscola senza valore`}):(E.sort((A,S)=>t.getDiscardOrderScore(A)-t.getDiscardOrderScore(S)),console.log(`[COLLABORATIVA AVANZATA] ⚠️ PRESA 0 PUNTI - SACRIFICIO MINIMO: ${E[0].rank} di ${E[0].suit}`),{strategy:"support",recommendedCard:E[0],reason:`⚠️ SACRIFICIO MINIMO: Presa da 0 punti → sacrifico ${E[0].rank} non-briscola per conservare briscole`})}}else{console.log("[COLLABORATIVA AVANZATA] 😞 CASO ESTREMO: Solo briscole per presa da 0 punti - uso la più debole");const y=n.filter(p=>p.suit===r.trumpSuit),$=y.filter(p=>t.getCardValue(p)===0);return $.length>0?($.sort((p,A)=>t.getCardStrengthScore(p)-t.getCardStrengthScore(A)),console.log(`[COLLABORATIVA AVANZATA] 😞 BRISCOLA DEBOLE FORZATA: ${$[0].rank} di ${$[0].suit}`),{strategy:"support",recommendedCard:$[0],reason:`😞 FORZATURA: Presa da 0 punti ma solo briscole disponibili → uso la più debole ${$[0].rank}`}):(y.sort((p,A)=>t.getDiscardOrderScore(p)-t.getDiscardOrderScore(A)),console.log(`[COLLABORATIVA AVANZATA] 😢 SPRECO FORZATO: ${y[0].rank} di ${y[0].suit} per presa da 0 punti`),{strategy:"support",recommendedCard:y[0],reason:`😢 SPRECO INEVITABILE: Presa da 0 punti, solo briscole con valore disponibili → uso ${y[0].rank}`})}}const T=n.filter(E=>t.getCardValue(E)===0);if(T.length>0){const E=T.reduce((y,$)=>t.getCardOrder($)<t.getCardOrder(y)?$:y);return{strategy:"support",recommendedCard:E,reason:`🛡️ PROTEZIONE TEAM: Avversari vincenti con ${s.toFixed(1)} punti, gioco carta senza punti (${E.rank})`}}}if(!i.shouldSupport)return console.log("[COLLABORATIVA AVANZATA] ⚡ Nessun supporto necessario - strategia neutra"),{strategy:"neutral"};if(i.teammateIsWinning){console.log("[COLLABORATIVA AVANZATA] 🏆 COMPAGNO STA VINCENDO - Modalità supporto attivo!");const T=i.winningCard;if(!T)return{strategy:"support"};if(me(T,o,r)){console.log("[COLLABORATIVA AVANZATA] 🔥 COMPAGNO IMBATTIBILE - Preparazione valorizzazione!");const p=n.filter(A=>{var S,I;return!t.canWinCurrentTrick(A,r.currentTrick||[],((I=(S=r.currentTrick)==null?void 0:S[0])==null?void 0:I.suit)||null,r.trumpSuit)});if(p.length>0){const A=p[0];return{strategy:"support",recommendedCard:A,reason:`🤝 SUPPORTO SICURO: Compagno imbattibile, non interferisco con ${A.rank}`}}}const y=n.filter(p=>T.suit===r.trumpSuit?p.suit===r.trumpSuit?t.getCardOrder(p)<=t.getCardOrder(T):!0:p.suit===r.trumpSuit?!1:p.suit===T.suit?t.getCardOrder(p)<=t.getCardOrder(T):!0);if(y.length===0)return{strategy:"support",recommendedCard:n.reduce((A,S)=>{const I=S.suit===r.trumpSuit?1e3+t.getCardOrder(S):t.getCardOrder(S),b=A.suit===r.trumpSuit?1e3+t.getCardOrder(A):t.getCardOrder(A);return I<b?S:A}),reason:"Giocando la carta meno competitiva per non danneggiare il compagno"};if(a&&(s>=1||g)){console.log(`[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + COMPAGNO VINCENTE + PRESA PREZIOSA (${s.toFixed(1)} punti)`);const p=K(y);if(p.length>0){const I=p.reduce((b,F)=>t.getCardValue(F)>t.getCardValue(b)?F:b);return console.log(`[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE FIGURE: ${I.rank} di ${I.suit} (${t.getCardValue(I)} punti)`),{strategy:"support",recommendedCard:I,reason:`🏆 VALORIZZAZIONE FIGURE: Ultimo giocatore + team vincente, gioco ${I.rank} (${t.getCardValue(I)} punti)`}}const A=y.filter(I=>I.suit===r.trumpSuit&&(I.rank==="7"||I.rank==="6"||I.rank==="5"||I.rank==="4"));if(A.length>0&&s>=1){const I=A.filter(b=>{var F,D;return t.canWinCurrentTrick(b,r.currentTrick||[],((D=(F=r.currentTrick)==null?void 0:F[0])==null?void 0:D.suit)||null,r.trumpSuit)});if(I.length>0){I.sort((F,D)=>t.getCardStrengthScore(F)-t.getCardStrengthScore(D));const b=I[0];return console.log(`[COLLABORATIVA AVANZATA] 🚨 CORREZIONE CRITICA: Prendo con briscola bassa ${b.rank} di ${b.suit} per salvare ${s.toFixed(1)} punti!`),{strategy:"support",recommendedCard:b,reason:`🚨 SALVATAGGIO PUNTI: Ultimo giocatore, uso briscola bassa ${b.rank} per salvare ${s.toFixed(1)} punti del team!`}}}const S=y.filter(I=>_(I));if(S.length>0){S.sort((b,F)=>t.getCardValue(F)-t.getCardValue(b));const I=S[0];return console.log(`[COLLABORATIVA AVANZATA] 💰 FALLBACK PUNTI: ${I.rank} di ${I.suit} (${t.getCardValue(I)} punti)`),{strategy:"support",recommendedCard:I,reason:`💰 PUNTI FALLBACK: Ultimo giocatore, valorizzo con ${I.rank} (${t.getCardValue(I)} punti)`}}}if(s>=1){console.log(`[COLLABORATIVA AVANZATA] 🎯 NON-ULTIMO + COMPAGNO VINCENTE + PRESA PREZIOSA (${s.toFixed(1)} punti)`);const p=y.filter(A=>t.getCardValue(A)>0&&(A.rank==="A"||A.rank==="K"||A.rank==="H"||A.rank==="J"));if(p.length>0){p.sort((S,I)=>t.getCardValue(I)-t.getCardValue(S));const A=p[0];return console.log(`[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE NON-ULTIMO: ${A.rank} di ${A.suit} (${t.getCardValue(A)} punti)`),{strategy:"support",recommendedCard:A,reason:`🏆 VALORIZZAZIONE FIGURE: Compagno vincente + presa preziosa, gioco ${A.rank} (${t.getCardValue(A)} punti)`}}}return{strategy:"support",recommendedCard:y.reduce((p,A)=>{const S=Z(A,o,r),I=Z(p,o,r);return S<I?A:p}),reason:"Supportando il compagno con carta di scarto"}}return{strategy:"compete"}},Or={[G.EASY]:{errorRate:.4,cooperationRate:.6,strategicRate:.3,memoryUsage:.4},[G.MEDIUM]:{errorRate:.1,cooperationRate:.9,strategicRate:.6,memoryUsage:.7},[G.HARD]:{errorRate:0,cooperationRate:1,strategicRate:1,memoryUsage:1}},Sr=(r,e,n,o)=>{var l,c;const t=Or[o],i=((c=(l=e.players)==null?void 0:l[n])==null?void 0:c.team)||"UNKNOWN",a=`CPU${n} (Team ${i}) [${o}]`;if(console.log(`
🤖 [UNIFIED AI] ${a} - Thinking...`),!e.currentTrick||e.currentTrick.length===0){const d=e.trickNumber??1;if(d===1&&e.trumpSuit&&e.lastTrumpSelector===n){const f=r.some(O=>O.suit===e.trumpSuit&&O.rank==="A"),C=r.some(O=>O.suit===e.trumpSuit&&O.rank==="2"),P=r.some(O=>O.suit===e.trumpSuit&&O.rank==="3");if(f&&C&&P){const O=r.find(k=>k.suit===e.trumpSuit&&k.rank==="A");if(O)return console.log(`[UNIFIED AI] 🎯🎯🎯 MARAFFA OBBLIGATORIA! Gioco Asso di ${O.suit} per 3 punti bonus!`),O}}const h=d<=3?.8:t.strategicRate;if(Math.random()<h){console.log(`[UNIFIED AI] 🎯 Tentativo apertura strategica (${(h*100).toFixed(0)}%)`);const f=r.filter(C=>C.rank==="3"&&C.suit!==e.trumpSuit);if(f.length>0){const C=f[0];return console.log(`[UNIFIED AI] ✅ Apertura con 3 di ${C.suit}`),C}}}const s=!e.currentTrick||e.currentTrick.length===0;if(s&&Math.random()<t.cooperationRate){const d=mr(r,e,n);if(d.shouldPlaySuit&&d.recommendedCard)return console.log(`[UNIFIED AI] 🎯 BUSSO FOLLOW-UP (${(t.cooperationRate*100).toFixed(0)}%): ${d.reason}`),d.recommendedCard}if(Math.random()<t.cooperationRate){const d=se(e),m=Tr(e,n,r,d);if(m.strategy==="support"&&m.recommendedCard)return console.log(`[UNIFIED AI] 🤝 SUPPORTO TEAM (${(t.cooperationRate*100).toFixed(0)}%): ${m.reason}`),m.recommendedCard;if(m.strategy==="compete"&&m.recommendedCard)if(s){const f=new x().getCardValue(m.recommendedCard),C=["4","5","6","7"].includes(m.recommendedCard.rank),P=m.recommendedCard.suit===e.trumpSuit;if(f>0&&!C&&!P)console.log(`[UNIFIED AI] ⚠️ BLOCCO strategia cooperativa - carta con punti (${m.recommendedCard.rank} di ${m.recommendedCard.suit}) non appropriata per primo turno`);else return console.log(`[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(t.cooperationRate*100).toFixed(0)}%): ${m.reason}`),m.recommendedCard}else return console.log(`[UNIFIED AI] 🚨 RECUPERO AGGRESSIVO (${(t.cooperationRate*100).toFixed(0)}%): ${m.reason}`),m.recommendedCard}if(e.currentTrick&&e.currentTrick.length>0&&e.currentTrick.some(m=>m&&m.rank==="A")){console.log("[UNIFIED AI] 🔥 ASSO SUL TAVOLO! Cerco di prenderlo...");const m=new x,h=r.filter(f=>{const C=[...e.currentTrick.filter(Boolean),f];return m.canWinCurrentTrick(f,C,e.leadSuit,e.trumpSuit)});if(h.length>0)if(Math.random()>t.errorRate){const f=h[0];return console.log(`[UNIFIED AI] 🔥 PRENDO ASSO CON ${f.rank} di ${f.suit}!`),f}else console.log(`[UNIFIED AI] 😵 ERRORE: Non riesco a prendere l'asso (${(t.errorRate*100).toFixed(0)}% errore)`)}if(Math.random()<t.memoryUsage){console.log(`[UNIFIED AI] 🧠 Uso analisi memoria (${(t.memoryUsage*100).toFixed(0)}%)`);const d=pr(r,e.currentTrick||[],e,n);if(d.optimalCards.length>0&&Math.random()>t.errorRate){const m=d.optimalCards[0];if((!e.currentTrick||e.currentTrick.length===0)&&m.suit===e.trumpSuit){const f=r.filter(C=>C.suit===e.trumpSuit);if(f.length<4)console.log(`[UNIFIED AI] ⚠️ Memoria suggerisce briscola ${m.rank}, ma ho solo ${f.length} briscole - ignoro suggerimento`);else return console.log(`[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${m.rank} di ${m.suit}`),m}else return console.log(`[UNIFIED AI] ✅ Carta ottimale dalla memoria: ${m.rank} di ${m.suit}`),m}}if(Math.random()<t.errorRate){console.log(`[UNIFIED AI] 😵 ERRORE CASUALE (${(t.errorRate*100).toFixed(0)}% probabilità)`);const d=r[Math.floor(Math.random()*r.length)];return console.log(`[UNIFIED AI] 🎲 Gioco carta casuale: ${d.rank} di ${d.suit}`),d}if(console.log("[UNIFIED AI] 🎯 Logica ottimale"),!e.currentTrick||e.currentTrick.length===0){console.log("[UNIFIED AI] 🎯 PRIMO DEL TURNO - Strategia apertura intelligente");const d=new x,m=Re(e);r.filter(w=>w.suit===e.trumpSuit);const h=dr(r,e);let f=r;h.shouldConserve&&h.cardToAvoid&&(f=r.filter(w=>{var N,L;return!(w.suit===((N=h.cardToAvoid)==null?void 0:N.suit)&&w.rank===((L=h.cardToAvoid)==null?void 0:L.rank))}),console.log(`[UNIFIED AI] 🎯 ${h.reason}`));const C=f.filter(w=>{if((w.rank==="A"||w.rank==="K")&&w.suit!==e.trumpSuit)return!1;if(w.suit===e.trumpSuit){const N=r.filter(L=>L.suit===e.trumpSuit);if(N.length<4)return console.log(`[UNIFIED AI] ⚠️ Evito briscola ${w.rank} come apertura - ho solo ${N.length} briscole`),!1}return ir(w,m,e.trumpSuit)});if(C.length>0){const w=gr(C,m);if(w.length>0){const N=w[0];return console.log(`[UNIFIED AI] ✅ CARTA SICURA: ${N.rank} di ${N.suit}`),N}}const P=lr(f,e);if(P.shouldPlayTrump&&P.recommendedCard)return console.log(`[UNIFIED AI] 🎯 ${P.reason}`),P.recommendedCard;const O=f.filter(w=>{const N=d.getCardValue(w)===0,L=w.suit!==e.trumpSuit,V=["4","5","6","7"].includes(w.rank);return N&&L&&V});if(O.length>0){const w=O.reduce((N,L)=>d.getCardOrder(L)>d.getCardOrder(N)?L:N);return console.log(`[UNIFIED AI] ✅ CARTA LISCIA: ${w.rank} di ${w.suit} (nessun punto da perdere)`),w}const k=f.filter(w=>!((w.rank==="A"||w.rank==="K")&&w.suit!==e.trumpSuit)),M=(k.length>0?k:f).reduce((w,N)=>d.getCardValue(N)<d.getCardValue(w)?N:w);return console.log(`[UNIFIED AI] ⚠️ FALLBACK: ${M.rank} di ${M.suit} (minor rischio, evito assi/re non-briscola)`),M}const u=new x,g=r.reduce((d,m)=>u.getCardValue(m)<u.getCardValue(d)?m:d);return console.log(`[UNIFIED AI] 📉 Carta di minor valore: ${g.rank} di ${g.suit}`),g};class Ir{constructor(){ee(this,"playedCards",[]);ee(this,"gameRound",0);this.playedCards=[]}startNewRound(){this.playedCards=[],this.gameRound++}recordPlayedCard(e,n){this.playedCards.push(e)}getPlayedCards(){return[...this.playedCards]}isCardPlayed(e){return this.playedCards.some(n=>n.suit===e.suit&&n.rank===e.rank)}updateMemory(e){}reset(){this.playedCards=[],this.gameRound=0}}const kr=new Ir,yr=(r,e,n)=>{var u,g;const o=((g=(u=r.players)==null?void 0:u[e])==null?void 0:g.team)||"UNKNOWN",t=`CPU${e} (Team ${o}) [${n}]`;if(console.log(`
🤖 ===== ${t} STA PENSANDO... =====`),!r||!r.players||!r.players[e]||!r.players[e].hand)return console.log(`❌ ${t} - ERRORE: dati giocatore non validi`),null;let a=[...r.players[e].hand];if(!r.currentTrick||r.currentTrick.length===0){if((r.trickNumber??1)===1&&r.trumpSuit&&r.lastTrumpSelector===e){const d=a.some(f=>f.suit===r.trumpSuit&&f.rank==="A"),m=a.some(f=>f.suit===r.trumpSuit&&f.rank==="2"),h=a.some(f=>f.suit===r.trumpSuit&&f.rank==="3");if(d&&m&&h){const f=a.find(C=>C.suit===r.trumpSuit&&C.rank==="A");if(f)return console.log(`🎯🎯🎯 ${t} - MARAFFA OBBLIGATORIA! Gioco Asso di ${f.suit} per 3 punti bonus!`),f}}console.log(`🎯 ${t} - PRIMO DEL TURNO - CONTROLLO 3 NON-BRISCOLA!`);const c=a.filter(d=>d.rank==="3"&&d.suit!==r.trumpSuit);if(c.length>0){const d=c[0];return console.log(`🎯 ${t} - STRATEGIA APERTURA: GIOCO 3 di ${d.suit}!`),d}}if(r.leadSuit&&r.currentTrick&&r.currentTrick.length>0){const l=a.filter(c=>c.suit===r.leadSuit);l.length>0&&(a=l,console.log(`🎴 ${t} - Devo seguire il seme: ${r.leadSuit} (${l.length} carte)`))}if(a.length===0)return console.log(`❌ ${t} - ERRORE: nessuna carta disponibile`),null;if(r.currentTrick&&r.currentTrick.length>0&&r.currentTrick.some(c=>c&&c.rank==="A")){console.log(`🔥 ${t} - ASSO SUL TAVOLO! Cerco di prenderlo...`);const c=new x,d=a.filter(m=>{const h=[...r.currentTrick,m];return c.canWinCurrentTrick(m,h,r.leadSuit,r.trumpSuit)});if(d.length>0){const m=d[0];return console.log(`🔥 ${t} - PRENDO ASSO CON ${m.rank} di ${m.suit}!`),m}}try{kr.updateMemory(r)}catch(l){console.log(`⚠️ ${t} - Errore aggiornamento memoria:`,l)}console.log(`🎯 ${t} - STRATEGIA UNIFICATA [${n}]`);const s=Sr(a,r,e,n);return console.log(`✅ ${t} - DECISIONE FINALE: ${(s==null?void 0:s.rank)||"NULL"} di ${(s==null?void 0:s.suit)||"NULL"}`),s},Rr=(r,e,n,o)=>{if(r.currentTrick.length>0)return{shouldAnnounce:!1,announcement:null,reason:"Non è il primo del turno"};const t=r.players[e],i=ze(t,r,n,o);return i?(console.log(`[AI STRATEGIC] Player ${e} (${t.name}) considera dichiarazione: ${i}`),{shouldAnnounce:!0,announcement:i,reason:`Dichiarazione strategica: ${i} (${n.rank} di ${n.suit})`}):{shouldAnnounce:!1,announcement:null,reason:"Nessuna dichiarazione strategica appropriata"}},$r=(r,e,n)=>{const o=qe(r,e,n);let t=.5;return o.strategy.includes("BUSSO")?t=.9:o.strategy.includes("VOLO")?t=.8:o.strategy.includes("STRISCIO")&&(t=.6),console.log(`[AI ANNOUNCEMENT STRATEGY] ${o.strategy} (Confidenza: ${(t*100).toFixed(0)}%)`),{filteredCards:o.recommendedCards,strategy:o.strategy,confidence:t}},Er=(r,e,n,o)=>{if(!n)return;const t={type:n,playerIndex:e,suit:o,trickNumber:r.trickNumber};Ke(t,r);const i=r.players[e];console.log(`[AI MEMORY UPDATE] Processed announcement: ${i.name} declares ${n.toUpperCase()} on ${o}`)},Pr=(r,e,n,o)=>{if((!e.currentTrick||e.currentTrick.length===0)&&r.filter(s=>s.rank==="3"&&s.suit!==e.trumpSuit).length>0)return console.log("[STRATEGIC HANDLER] 🎯🎯🎯 PRIMO DEL TURNO CON 3 NON-BRISCOLA - BLOCCO FILTRI! 🎯🎯🎯"),{cards:r,strategicInfo:"PRIORITÀ ASSOLUTA: 3 non-briscola disponibile - no filtri strategici",useAnnouncement:!1};const i=$r(r,e,n);return i.confidence>=.7?{cards:i.filteredCards,strategicInfo:`Strategia basata su dichiarazione: ${i.strategy}`,useAnnouncement:!0}:{cards:r,strategicInfo:"Strategia normale - no dichiarazioni influenti",useAnnouncement:!1}},Ur=(r,e,n,o,t,i)=>{const a=r.players[r.currentPlayer];if(!a||!a.hand){console.error("Errore: giocatore corrente o mano non definiti");return}const s=De(a.hand,r.leadSuit);s.length>0&&u(r);function u(l){var f;const c=e==="easy"?G.EASY:e==="medium"?G.MEDIUM:G.HARD,d=Pr(s,l,l.currentPlayer);console.log(`[AI STRATEGIC HANDLER] ${d.strategicInfo}`);const m=[...a.hand];if(d.useAnnouncement){const C=d.cards;a.hand=a.hand.filter(P=>C.some(O=>O.id===P.id)),console.log(`[AI STRATEGIC] Filtering hand from ${m.length} to ${a.hand.length} cards`)}const h=yr(l,l.currentPlayer,c);if(a.hand=m,h){if(l.currentTrick.length===0){const C=Rr(l,l.currentPlayer,h,e);if(C.shouldAnnounce){console.log(`[AI ANNOUNCEMENT] ${a.name}: ${(f=C.announcement)==null?void 0:f.toUpperCase()}`),console.log(`[AI ANNOUNCEMENT] Ragione: ${C.reason}`);const P=ke(l,C.announcement);i&&(i({type:C.announcement,playerIndex:l.currentPlayer}),setTimeout(()=>{i({type:null,playerIndex:-1})},2500)),Er(P,l.currentPlayer,C.announcement,h.suit),setTimeout(()=>{g(P,h)},1e3);return}}g(l,h)}else if(console.error("L'AI non è riuscita a scegliere una carta valida in modalità",e),s.length>0){re.playSound("cardSnap",{playerId:l.currentPlayer});const C=oe(l,s[0].id);o(s[0]),n(C)}}function g(l,c){re.playSound("cardPlay",{playerId:l.currentPlayer});const d=oe(l,c.id);o(c),d.currentTrick.length===0&&l.currentTrick.length===3?(setTimeout(()=>re.playSound("cardGather"),500),t(!0),setTimeout(()=>{t(!1),o(null),n(d)},2800)):n(d)}};export{q as C,R,U as S,Ae as W,ke as a,nr as b,or as c,tr as d,We as e,Lr as f,De as g,Ur as h,xe as i,Y as j,Vr as k,Ie as l,br as m,wr as n,oe as p,pe as r,X as s};
