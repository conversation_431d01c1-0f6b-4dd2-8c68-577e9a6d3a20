import{W as r}from"./game-DnO6rJ4w.js";import{VERIFICATION_RESULT as o,WebPurchaseRedemptionResultType as i,REFUND_REQUEST_STATUS as t}from"./index-CrAX9mSl.js";import"./audioManager-oMWoQbcF.js";import"./audio-5UOY9KLB.js";import"./vendor-COrNHRvO.js";import"./supabase-DLIhrfaA.js";class f extends r{constructor(){super(...arguments),this.shouldMockWebResults=!1,this.webNotSupportedErrorMessage="Web not supported in this plugin.",this.mockEmptyCustomerInfo={entitlements:{all:{},active:{},verification:o.NOT_REQUESTED},activeSubscriptions:[],allPurchasedProductIdentifiers:[],latestExpirationDate:null,firstSeen:"2023-08-31T15:11:21.445Z",originalAppUserId:"mock-web-user-id",requestDate:"2023-08-31T15:11:21.445Z",allExpirationDates:{},allPurchaseDates:{},originalApplicationVersion:null,originalPurchaseDate:null,managementURL:null,nonSubscriptionTransactions:[],subscriptionsByProductIdentifier:{}}}configure(e){return this.mockNonReturningFunctionIfEnabled("configure")}parseAsWebPurchaseRedemption(e){return this.mockReturningFunctionIfEnabled("parseAsWebPurchaseRedemption",{webPurchaseRedemption:null})}redeemWebPurchase(e){return this.mockReturningFunctionIfEnabled("redeemWebPurchase",{result:i.INVALID_TOKEN})}setMockWebResults(e){return this.shouldMockWebResults=e.shouldMockWebResults,Promise.resolve()}setSimulatesAskToBuyInSandbox(e){return this.mockNonReturningFunctionIfEnabled("setSimulatesAskToBuyInSandbox")}addCustomerInfoUpdateListener(e){return this.mockReturningFunctionIfEnabled("addCustomerInfoUpdateListener","mock-callback-id")}removeCustomerInfoUpdateListener(e){return this.mockReturningFunctionIfEnabled("removeCustomerInfoUpdateListener",{wasRemoved:!1})}addShouldPurchasePromoProductListener(e){return this.mockReturningFunctionIfEnabled("addShouldPurchasePromoProductListener","mock-callback-id")}removeShouldPurchasePromoProductListener(e){return this.mockReturningFunctionIfEnabled("removeShouldPurchasePromoProductListener",{wasRemoved:!1})}getOfferings(){const e={all:{},current:null};return this.mockReturningFunctionIfEnabled("getOfferings",e)}getCurrentOfferingForPlacement(e){return this.mockReturningFunctionIfEnabled("getCurrentOfferingForPlacement",null)}syncAttributesAndOfferingsIfNeeded(){const e={all:{},current:null};return this.mockReturningFunctionIfEnabled("syncAttributesAndOfferingsIfNeeded",e)}getProducts(e){const n={products:[]};return this.mockReturningFunctionIfEnabled("getProducts",n)}purchaseStoreProduct(e){const n={productIdentifier:e.product.identifier,customerInfo:this.mockEmptyCustomerInfo,transaction:this.mockTransaction(e.product.identifier)};return this.mockReturningFunctionIfEnabled("purchaseStoreProduct",n)}purchaseDiscountedProduct(e){const n={productIdentifier:e.product.identifier,customerInfo:this.mockEmptyCustomerInfo,transaction:this.mockTransaction(e.product.identifier)};return this.mockReturningFunctionIfEnabled("purchaseDiscountedProduct",n)}purchasePackage(e){const n={productIdentifier:e.aPackage.product.identifier,customerInfo:this.mockEmptyCustomerInfo,transaction:this.mockTransaction(e.aPackage.product.identifier)};return this.mockReturningFunctionIfEnabled("purchasePackage",n)}purchaseSubscriptionOption(e){const n={productIdentifier:e.subscriptionOption.productId,customerInfo:this.mockEmptyCustomerInfo,transaction:this.mockTransaction(e.subscriptionOption.productId)};return this.mockReturningFunctionIfEnabled("purchaseSubscriptionOption",n)}purchaseDiscountedPackage(e){const n={productIdentifier:e.aPackage.product.identifier,customerInfo:this.mockEmptyCustomerInfo,transaction:this.mockTransaction(e.aPackage.product.identifier)};return this.mockReturningFunctionIfEnabled("purchaseDiscountedPackage",n)}restorePurchases(){const e={customerInfo:this.mockEmptyCustomerInfo};return this.mockReturningFunctionIfEnabled("restorePurchases",e)}recordPurchase(e){const n={transaction:this.mockTransaction(e.productID)};return this.mockReturningFunctionIfEnabled("recordPurchase",n)}getAppUserID(){return this.mockReturningFunctionIfEnabled("getAppUserID",{appUserID:"test-web-user-id"})}getStorefront(){return this.mockReturningFunctionIfEnabled("getStorefront",{countryCode:"USA"})}logIn(e){const n={customerInfo:this.mockEmptyCustomerInfo,created:!1};return this.mockReturningFunctionIfEnabled("logIn",n)}logOut(){const e={customerInfo:this.mockEmptyCustomerInfo};return this.mockReturningFunctionIfEnabled("logOut",e)}setLogLevel(e){return this.mockNonReturningFunctionIfEnabled("setLogLevel")}setLogHandler(e){return this.mockNonReturningFunctionIfEnabled("setLogHandler")}getCustomerInfo(){const e={customerInfo:this.mockEmptyCustomerInfo};return this.mockReturningFunctionIfEnabled("getCustomerInfo",e)}syncPurchases(){return this.mockNonReturningFunctionIfEnabled("syncPurchases")}syncObserverModeAmazonPurchase(e){return this.mockNonReturningFunctionIfEnabled("syncObserverModeAmazonPurchase")}syncAmazonPurchase(e){return this.mockNonReturningFunctionIfEnabled("syncAmazonPurchase")}enableAdServicesAttributionTokenCollection(){return this.mockNonReturningFunctionIfEnabled("enableAdServicesAttributionTokenCollection")}isAnonymous(){const e={isAnonymous:!1};return this.mockReturningFunctionIfEnabled("isAnonymous",e)}checkTrialOrIntroductoryPriceEligibility(e){return this.mockReturningFunctionIfEnabled("checkTrialOrIntroductoryPriceEligibility",{})}getPromotionalOffer(e){return this.mockReturningFunctionIfEnabled("getPromotionalOffer",void 0)}getEligibleWinBackOffersForProduct(e){return this.mockReturningFunctionIfEnabled("getEligibleWinBackOffersForProduct",{eligibleWinBackOffers:[]})}getEligibleWinBackOffersForPackage(e){return this.mockReturningFunctionIfEnabled("getEligibleWinBackOffersForPackage",{eligibleWinBackOffers:[]})}purchaseProductWithWinBackOffer(e){return this.mockReturningFunctionIfEnabled("purchaseProductWithWinBackOffer",void 0)}purchasePackageWithWinBackOffer(e){return this.mockReturningFunctionIfEnabled("purchasePackageWithWinBackOffer",void 0)}invalidateCustomerInfoCache(){return this.mockNonReturningFunctionIfEnabled("invalidateCustomerInfoCache")}presentCodeRedemptionSheet(){return this.mockNonReturningFunctionIfEnabled("presentCodeRedemptionSheet")}setAttributes(e){return this.mockNonReturningFunctionIfEnabled("setAttributes")}setEmail(e){return this.mockNonReturningFunctionIfEnabled("setEmail")}setPhoneNumber(e){return this.mockNonReturningFunctionIfEnabled("setPhoneNumber")}setDisplayName(e){return this.mockNonReturningFunctionIfEnabled("setDisplayName")}setPushToken(e){return this.mockNonReturningFunctionIfEnabled("setPushToken")}setProxyURL(e){return this.mockNonReturningFunctionIfEnabled("setProxyURL")}collectDeviceIdentifiers(){return this.mockNonReturningFunctionIfEnabled("collectDeviceIdentifiers")}setAdjustID(e){return this.mockNonReturningFunctionIfEnabled("setAdjustID")}setAppsflyerID(e){return this.mockNonReturningFunctionIfEnabled("setAppsflyerID")}setFBAnonymousID(e){return this.mockNonReturningFunctionIfEnabled("setFBAnonymousID")}setMparticleID(e){return this.mockNonReturningFunctionIfEnabled("setMparticleID")}setCleverTapID(e){return this.mockNonReturningFunctionIfEnabled("setCleverTapID")}setMixpanelDistinctID(e){return this.mockNonReturningFunctionIfEnabled("setMixpanelDistinctID")}setFirebaseAppInstanceID(e){return this.mockNonReturningFunctionIfEnabled("setFirebaseAppInstanceID")}setOnesignalID(e){return this.mockNonReturningFunctionIfEnabled("setOnesignalID")}setOnesignalUserID(e){return this.mockNonReturningFunctionIfEnabled("setOnesignalUserID")}setAirshipChannelID(e){return this.mockNonReturningFunctionIfEnabled("setAirshipChannelID")}setMediaSource(e){return this.mockNonReturningFunctionIfEnabled("setMediaSource")}setCampaign(e){return this.mockNonReturningFunctionIfEnabled("setCampaign")}setAdGroup(e){return this.mockNonReturningFunctionIfEnabled("setAdGroup")}setAd(e){return this.mockNonReturningFunctionIfEnabled("setAd")}setKeyword(e){return this.mockNonReturningFunctionIfEnabled("setKeyword")}setCreative(e){return this.mockNonReturningFunctionIfEnabled("setCreative")}canMakePayments(e){return this.mockReturningFunctionIfEnabled("canMakePayments",{canMakePayments:!0})}beginRefundRequestForActiveEntitlement(){const e={refundRequestStatus:t.USER_CANCELLED};return this.mockReturningFunctionIfEnabled("beginRefundRequestForActiveEntitlement",e)}beginRefundRequestForEntitlement(e){const n={refundRequestStatus:t.USER_CANCELLED};return this.mockReturningFunctionIfEnabled("beginRefundRequestForEntitlement",n)}beginRefundRequestForProduct(e){const n={refundRequestStatus:t.USER_CANCELLED};return this.mockReturningFunctionIfEnabled("beginRefundRequestForProduct",n)}showInAppMessages(e){return this.mockNonReturningFunctionIfEnabled("showInAppMessages")}isConfigured(){const e={isConfigured:!0};return this.mockReturningFunctionIfEnabled("isConfigured",e)}mockTransaction(e){return{productIdentifier:e,purchaseDate:new Date().toISOString(),transactionIdentifier:""}}mockNonReturningFunctionIfEnabled(e){return this.shouldMockWebResults?(console.log(`${e} called on web with mocking enabled. No-op`),Promise.resolve()):Promise.reject(this.webNotSupportedErrorMessage)}mockReturningFunctionIfEnabled(e,n){return this.shouldMockWebResults?(console.log(`${e} called on web with mocking enabled. Returning mocked value`),Promise.resolve(n)):Promise.reject(this.webNotSupportedErrorMessage)}}export{f as PurchasesWeb};
